"""
Configuration file for the Live Shoplifting Detection System
"""

import os

class LiveConfig:
    """Configuration class for live video processing"""
    
    # Hardware Configuration
    WEBCAM_ID = 0  # Default webcam (try 1, 2, etc. if 0 doesn't work)
    
    # Model Paths
    ACTION_TRANSFORMER_MODEL = 'data/models/trained_model_224.pt'
    YOLO_MODEL = 'data/models/yolo11s.pt'
    
    # Output Configuration
    OUTPUT_DIR = 'data/output/live_clips'  # Base directory
    SAVE_CLIPS = True  # Set to False to not save clips (saves storage)

    # Folder Structure (with timestamped sessions):
    # OUTPUT_DIR/
    # ├── dd-mm-yyyy_h-m-s/     <- Session folder (timestamp when started)
    # │   ├── feed/             <- Individual person clips from webcam
    # │   └── annotated/        <- Full-frame clips with bounding boxes
    # ├── dd-mm-yyyy_h-m-s/     <- Another session folder
    # │   ├── feed/
    # │   └── annotated/
    # └── ...
    
    # Processing Configuration
    FRAME_BUFFER_SIZE = 16  # Number of frames per clip
    CROP_SIZE = (224, 224)  # Size of person crops (width, height)
    CONFIDENCE_THRESHOLD = 0.1  # YOLO confidence threshold (0.0-1.0)
    
    # Performance Configuration
    WEBCAM_WIDTH = 640
    WEBCAM_HEIGHT = 480
    WEBCAM_FPS = 30
    PREDICTION_QUEUE_SIZE = 10  # Max clips waiting for prediction
    
    # Display Configuration
    SHOW_LIVE_FEED = True  # Show webcam feed window
    PRINT_STATISTICS = True  # Print real-time statistics
    STATISTICS_INTERVAL = 5  # Seconds between statistics updates
    
    # Alert Configuration
    SUSPICIOUS_THRESHOLD = 0.5  # Prediction threshold for suspicious behavior
    ENABLE_ALERTS = True  # Enable alert notifications

    # Session Management
    AUTO_COMBINE_CLIPS = True  # Automatically combine clips when session ends
    
    @classmethod
    def create_output_dir(cls):
        """Create output directory if it doesn't exist"""
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
    
    @classmethod
    def validate_models(cls):
        """Check if model files exist"""
        models = [cls.ACTION_TRANSFORMER_MODEL, cls.YOLO_MODEL]
        missing = [m for m in models if not os.path.exists(m)]
        
        if missing:
            raise FileNotFoundError(f"Missing model files: {missing}")
        
        return True
    
    @classmethod
    def get_processor_config(cls):
        """Get configuration dictionary for LiveVideoProcessor"""
        return {
            'webcam_id': cls.WEBCAM_ID,
            'model_path': cls.ACTION_TRANSFORMER_MODEL,
            'yolo_path': cls.YOLO_MODEL,
            'output_dir': cls.OUTPUT_DIR,
            'frame_buffer_size': cls.FRAME_BUFFER_SIZE,
            'crop_size': cls.CROP_SIZE,
            'confidence_threshold': cls.CONFIDENCE_THRESHOLD
        }


class PerformanceConfig(LiveConfig):
    """High-performance configuration for fast processing"""
    
    FRAME_BUFFER_SIZE = 8  # Smaller buffer for faster processing
    CROP_SIZE = (112, 112)  # Smaller crops for faster inference
    CONFIDENCE_THRESHOLD = 0.5  # Higher threshold for fewer detections
    WEBCAM_WIDTH = 320
    WEBCAM_HEIGHT = 240


class AccuracyConfig(LiveConfig):
    """High-accuracy configuration for better detection"""
    
    FRAME_BUFFER_SIZE = 32  # Larger buffer for more context
    CROP_SIZE = (224, 224)  # Full resolution crops
    CONFIDENCE_THRESHOLD = 0.05  # Lower threshold for more detections
    WEBCAM_WIDTH = 1280
    WEBCAM_HEIGHT = 720


class DemoConfig(LiveConfig):
    """Demo configuration for presentations"""
    
    CONFIDENCE_THRESHOLD = 0.3  # Balanced threshold
    SHOW_LIVE_FEED = True
    PRINT_STATISTICS = True
    STATISTICS_INTERVAL = 2  # More frequent updates
    SAVE_CLIPS = False  # Don't save clips during demo


# Default configuration
DEFAULT_CONFIG = LiveConfig

# Configuration presets
CONFIGS = {
    'default': LiveConfig,
    'performance': PerformanceConfig,
    'accuracy': AccuracyConfig,
    'demo': DemoConfig
}

def get_config(config_name='default'):
    """Get configuration by name"""
    if config_name not in CONFIGS:
        raise ValueError(f"Unknown config: {config_name}. Available: {list(CONFIGS.keys())}")
    
    return CONFIGS[config_name]
