# Live Shoplifting Detection System - Complete Documentation

A comprehensive real-time multi-threaded video processing system that captures live webcam feed, detects and tracks persons using YOLO, crops individual person clips, makes shoplifting predictions using a PyTorch ActionTransformer model, and creates annotated videos with bounding boxes.

## 📚 Table of Contents

### 🎥 Live Video Processing System
- [🏗️ Architecture](#️-architecture)
- [🚀 Quick Start](#-quick-start)
- [⚙️ Configuration Options](#️-configuration-options)
- [📊 Monitoring & Statistics](#-monitoring--statistics)
- [📁 File Structure](#-file-structure)
- [🎯 Key Features](#-key-features)
- [🛠️ Customization Examples](#️-customization-examples)
- [🐛 Troubleshooting](#-troubleshooting)

### 🎬 Video File Processing System
- [🚀 Quick Start - Video Processing](#-quick-start---video-processing)
- [⚙️ Video Processing Configuration](#️-video-processing-configuration)
- [📊 Video Processing Features](#-video-processing-features)
- [📁 Video Processing Output Structure](#-video-processing-output-structure)
- [🎯 Video Processing Workflow](#-video-processing-workflow)





---

# 🎥 Live Video Processing System

## 🏗️ Architecture

The system uses a **3-thread architecture** for optimal performance and real-time visualization:

### Thread 1: Video Capture & Clipping
- **Webcam Capture**: Continuously captures frames from webcam
- **Frame Buffering**: Maintains a rolling buffer of 16 frames
- **Clip Creation**: Creates 16-frame clips and sends to detection thread
- **Live Display**: Shows real-time feed with bounding boxes and predictions

### Thread 2: Person Detection & Cropping
- **YOLO Person Detection**: Detects and tracks persons in each 16-frame clip
- **Tracker Reset**: Automatically resets YOLO tracker for each new clip using `model.predictor.trackers[0].reset()`
- **Clean Person IDs**: Ensures fresh person ID assignment without carryover between clips
- **Person Cropping**: Extracts square crops of each detected person
- **Crop Saving**: Saves individual person clips for prediction
- **Detection Data**: Prepares data for prediction thread

### Thread 3: Prediction & Annotation
- **Prediction Processing**: Makes shoplifting/normal predictions using ActionTransformer
- **Bounding Box Drawing**: Draws colored bounding boxes (green=normal, red=suspicious)
- **Annotated Clips**: Creates and saves full-frame clips with bounding boxes and labels
- **Result Management**: Manages prediction results and live display updates

## 🚀 Quick Start

### 1. Basic Usage
```python
from live import LiveVideoProcessor

# Initialize processor
processor = LiveVideoProcessor()

# Start 3-thread processing
processor.start()

# System runs until you press 'q' in video window or Ctrl+C
# Watch live feed with real-time bounding boxes!
```

### 2. Run from Command Line
```bash
python live.py
```

### 3. Interactive Demo (Recommended)
```bash
python demo_3thread.py
```

### 4. Test the System
```bash
python test_live.py
```

### 5. Combine Session Clips
```bash
# Combine clips from all sessions
python combine_session_clips.py --all

# Combine clips from specific session
python combine_session_clips.py --session data/output/live_clips/18-06-2025_14-30-15

# List available sessions
python combine_session_clips.py --list
```

## ⚙️ Configuration Options

```python
processor = LiveVideoProcessor(
    webcam_id=0,                    # Webcam device ID
    model_path='data/models/trained_model_224.pt',  # ActionTransformer model
    yolo_path='data/models/yolo11s.pt',             # YOLO model
    output_dir='data/output',       # Directory to save clips
    frame_buffer_size=16,           # Number of frames per clip
    crop_size=(224, 224),          # Size of person crops
    confidence_threshold=0.1        # YOLO confidence threshold
)
```

## 📊 Monitoring & Statistics

### Get Real-time Statistics
```python
stats = processor.get_statistics()
print(stats)
# Output: {
#     'frames_processed': 1250,
#     'clips_created': 15,
#     'predictions_made': 12,
#     'pending_predictions': 2,
#     'results_available': 3,
#     'is_running': True
# }
```

### Get Latest Results
```python
results = processor.get_latest_results(max_results=5)
for result in results:
    status = "SUSPICIOUS" if result['is_suspicious'] else "NORMAL"
    print(f"Person {result['person_id']}: {status} (score: {result['prediction']:.3f})")
```

## 🔧 System Requirements

### Hardware
- **Webcam**: USB webcam or built-in camera
- **GPU**: NVIDIA GPU recommended for real-time processing
- **RAM**: 8GB+ recommended
- **Storage**: Space for temporary clip files

### Software Dependencies
- Python 3.8+
- PyTorch with CUDA support
- OpenCV
- Ultralytics YOLO
- NumPy
- All dependencies from `requirements.txt`

## 📁 File Structure

```
├── live.py                 # Main live processing system
├── demo_3thread.py        # Interactive 3-thread demo
├── test_live.py           # Test script
├── combine_session_clips.py # Session clip combination utility
├── config.py              # Configuration management
├── utils/                 # Utility modules
│   ├── actiontransformer.py # ActionTransformer model
│   ├── videodataset.py    # Video processing utilities
│   └── ...
├── data/
│   ├── models/
│   │   ├── trained_model_224.pt  # Trained ActionTransformer
│   │   └── yolo11s.pt           # YOLO model
│   └── output/
│       └── live_clips/          # Base output directory
│           ├── 18-06-2025_14-30-15/  # Session folder (timestamp)
│           │   ├── feed/        # Individual person clips (webcam crops)
│           │   └── annotated/   # Full-frame clips with bounding boxes
│           ├── 18-06-2025_15-45-22/  # Another session
│           │   ├── feed/
│           │   └── annotated/
│           └── ...
```

### 📂 Output Folder Organization

The system creates **timestamped session folders** to organize clips from different runs:

**📅 Session Folders** (format: `dd-mm-yyyy_h-m-s`):
- Each time you start the system, a new timestamped folder is created
- Example: `18-06-2025_14-30-15` for a session started on June 18, 2025 at 2:30:15 PM
- This keeps clips from different runs completely separate

**Within each session folder:**

- **`feed/`** subfolder contains:
  - Individual person clips cropped from webcam (`person_X_timestamp.mp4`)
  - These are the 224x224 crops used as input to the ActionTransformer model
  - Used for making predictions

- **`annotated/`** subfolder contains:
  - Full-frame clips with bounding boxes and predictions (`annotated_timestamp.mp4`)
  - Original webcam resolution with colored bounding boxes
  - Green boxes for normal behavior, red boxes for suspicious behavior
  - Includes prediction scores and person IDs as text overlays

- **Combined session video**:
  - Automatically created when session ends (`combined_session_timestamp.mp4`)
  - Contains all annotated clips from the session in chronological order
  - Saved directly in the session folder for easy access
  - Perfect for reviewing the entire session's activity

**Example folder structure:**
```
data/output/live_clips/
├── 18-06-2025_14-30-15/    # Morning session
│   ├── feed/               # Person clips from this session
│   ├── annotated/          # Annotated clips from this session
│   └── combined_session_18-06-2025_14-30-15.mp4  # Combined session video
├── 18-06-2025_16-45-30/    # Afternoon session
│   ├── feed/
│   ├── annotated/
│   └── combined_session_18-06-2025_16-45-30.mp4
└── 19-06-2025_09-15-45/    # Next day session
    ├── feed/
    ├── annotated/
    └── combined_session_19-06-2025_09-15-45.mp4
```

## 🎯 Key Features

### Real-time Processing
- **No Frame Drops**: Separate threads ensure continuous video capture
- **Efficient Buffering**: Rolling frame buffer for smooth processing
- **Asynchronous Predictions**: Predictions don't block video capture

### Person Tracking
- **YOLO Integration**: Uses YOLOv11 for person detection
- **Multi-person Support**: Tracks multiple persons simultaneously
- **ID Persistence**: Maintains person IDs across frames

### Intelligent Cropping
- **Automatic Sizing**: Crops are automatically resized to model input size
- **Boundary Handling**: Safe cropping within frame boundaries
- **Quality Control**: Filters out persons with insufficient frame coverage

### YOLO Tracker Management
- **Automatic Reset**: Tracker resets at the start of each new 16-frame clip
- **Clean ID Assignment**: Prevents person ID carryover between clips
- **Tracker Monitoring**: Real-time tracker status in system statistics
- **Error Handling**: Graceful handling of tracker reset failures

### Robust Prediction
- **Model Warmup**: Ensures consistent prediction timing
- **Error Handling**: Graceful handling of prediction failures
- **Result Tracking**: Comprehensive result logging and storage

## 🛠️ Customization Examples

### High-Performance Setup
```python
processor = LiveVideoProcessor(
    frame_buffer_size=8,        # Smaller buffer for faster processing
    confidence_threshold=0.5,   # Higher threshold for fewer false positives
    crop_size=(112, 112)       # Smaller crops for faster inference
)
```

### High-Accuracy Setup
```python
processor = LiveVideoProcessor(
    frame_buffer_size=32,       # Larger buffer for more context
    confidence_threshold=0.1,   # Lower threshold for more detections
    crop_size=(224, 224)       # Full resolution crops
)
```

## 🐛 Troubleshooting

### Common Issues

1. **Webcam Not Found**
   ```
   Error: Could not open webcam 0
   ```
   - Try different webcam IDs (0, 1, 2, etc.)
   - Check if webcam is being used by another application

2. **CUDA Out of Memory**
   ```
   RuntimeError: CUDA out of memory
   ```
   - Reduce `frame_buffer_size`
   - Use smaller `crop_size`
   - Close other GPU-intensive applications

3. **Slow Processing**
   - Increase `confidence_threshold` to reduce detections
   - Use GPU acceleration
   - Reduce webcam resolution

### Performance Tips

- **GPU Usage**: Ensure CUDA is available and models are on GPU
- **Queue Management**: Monitor queue sizes to prevent memory buildup
- **File Cleanup**: Uncomment clip deletion in prediction thread if storage is limited

## 🔄 Integration

The system can be easily integrated into larger applications:

```python
# Custom callback for predictions
def on_prediction(result):
    if result['is_suspicious']:
        send_alert(result)
        save_evidence(result['clip_path'])

# Modify prediction thread to call callback
# Add this in _prediction_thread_worker after prediction
on_prediction(result)
```

## 📈 Performance Metrics

Typical performance on modern hardware:
- **Frame Rate**: 20-30 FPS capture
- **Prediction Time**: 50-200ms per clip
- **Memory Usage**: 2-4GB RAM, 1-2GB VRAM
- **Latency**: 1-2 seconds from capture to prediction

## 🚦 System Status Indicators

- **✅ NORMAL**: Person behavior appears normal
- **🚨 SUSPICIOUS**: Potential shoplifting behavior detected
- **⚠️ WARNING**: System warnings (queue full, processing delays)
- **❌ ERROR**: System errors (model failures, hardware issues)

---

# 🎬 Video File Processing System

Process pre-recorded video files for shoplifting detection analysis with comprehensive output generation.

## 🚀 Quick Start - Video Processing

### 1. Basic Video Processing
```python
from video_processor import VideoProcessor

# Initialize processor
processor = VideoProcessor(
    video_path="path/to/your/video.mp4",
    model_path="data/models/trained_model_224.pt",
    output_dir="data/output/video_analysis"
)

# Process the entire video
processor.process_video()
```

### 2. Command Line Usage
```bash
# Process a video file
python video_processor.py path/to/video.mp4

# With custom output directory
python video_processor.py path/to/video.mp4 --output data/custom_output

# With specific time range
python video_processor.py path/to/video.mp4 --start-time 30 --end-time 120
```

### 3. Batch Processing
```bash
# Process multiple videos
python batch_process_videos.py data/videos/

# Process with custom settings
python batch_process_videos.py data/videos/ --confidence 0.3 --output data/batch_results
```

## ⚙️ Video Processing Configuration

```python
processor = VideoProcessor(
    video_path="input_video.mp4",           # Input video file
    model_path="data/models/trained_model_224.pt",  # ActionTransformer model
    yolo_path="data/models/yolo11s.pt",     # YOLO model
    output_dir="data/output",               # Output directory
    confidence_threshold=0.2,               # YOLO confidence threshold
    frame_skip=1,                          # Process every N frames (1=all frames)
    clip_length=16,                        # Frames per clip for analysis
    start_time=None,                       # Start time in seconds (None=beginning)
    end_time=None                          # End time in seconds (None=end)
)
```

## 📊 Video Processing Features

### Comprehensive Analysis
- **Person Detection**: YOLO-based person detection throughout video
- **Temporal Analysis**: 16-frame clips for behavior analysis
- **Prediction Scoring**: Shoplifting probability for each detected person
- **Timeline Generation**: Complete timeline of detections and predictions

### Output Generation
- **Annotated Video**: Full video with bounding boxes and predictions
- **Individual Clips**: Separate clips for each detected person
- **Analysis Report**: JSON report with all detections and predictions
- **Summary Statistics**: Overall video analysis summary

### Performance Optimization
- **Batch Processing**: Efficient processing of multiple videos
- **Memory Management**: Optimized for large video files
- **Progress Tracking**: Real-time progress updates
- **Error Recovery**: Robust error handling and recovery

## 📁 Video Processing Output Structure

```
data/output/video_analysis/
├── video_analysis_2025-06-23_14-30-15/    # Analysis session folder
│   ├── original_video_info.json           # Video metadata
│   ├── annotated_video.mp4                # Full video with annotations
│   ├── analysis_report.json               # Complete analysis results
│   ├── summary_statistics.json            # Summary stats
│   ├── person_clips/                      # Individual person clips
│   │   ├── person_1_00-15-23.mp4         # Person 1 clip (timestamp)
│   │   ├── person_2_00-15-45.mp4         # Person 2 clip
│   │   └── ...
│   ├── detection_frames/                  # Key detection frames
│   │   ├── detection_00-15-23.jpg        # Frame with detection
│   │   └── ...
│   └── timeline.json                      # Temporal analysis timeline
```

## 🎯 Video Processing Workflow

### 1. Video Analysis
```python
# Analyze video and get results
results = processor.process_video()

print(f"Total persons detected: {results['total_persons']}")
print(f"Suspicious activities: {results['suspicious_count']}")
print(f"Processing time: {results['processing_time']:.2f} seconds")
```

### 2. Custom Analysis
```python
# Process specific time range
processor.set_time_range(start_time=60, end_time=180)  # 1-3 minutes
results = processor.process_video()

# Process with custom confidence
processor.confidence_threshold = 0.5
results = processor.process_video()
```

### 3. Batch Analysis
```python
from batch_process_videos import BatchVideoProcessor

batch_processor = BatchVideoProcessor(
    input_dir="data/videos/",
    output_dir="data/batch_analysis/",
    model_path="data/models/trained_model_224.pt"
)

# Process all videos in directory
results = batch_processor.process_all_videos()
```

---

# 🎯 Additional Tools

The system includes additional optimization and processing tools:

## ⚡ Model Quantization
- **`simple_quantize.py`**: Easy model optimization for faster inference
- **`quantize_model.py`**: Advanced quantization with multiple methods
- **`load_quantized_model.py`**: Utilities for using optimized models

## 🔧 System Integration

The complete shoplifting detection system provides:

1. **🎥 Live Video Processing**: Real-time webcam analysis with 3-thread architecture
2. **🎬 Video File Processing**: Batch analysis of pre-recorded videos
3. **⚡ Model Optimization**: Tools for faster inference and deployment

### Development Workflow
```bash
# 1. Test live system
python demo_3thread.py

# 2. Process video files
python video_processor.py your_video.mp4

# 3. Combine and analyze results
python combine_session_clips.py --all
```

### Production Deployment
```python
from live import LiveVideoProcessor

# Deploy live system
processor = LiveVideoProcessor(
    model_path="data/models/trained_model_224.pt",
    confidence_threshold=0.3,
    output_dir="data/production_output"
)

processor.start()
```

## 📊 Performance Tips

### For Real-time Processing
- Adjust confidence thresholds for optimal detection
- Optimize frame buffer sizes for your hardware
- Monitor system resources during operation
- Use GPU acceleration when available

### For Batch Processing
- Process videos in parallel when possible
- Use appropriate hardware acceleration
- Implement progress tracking for long operations
- Handle large file processing efficiently

---

## 🎉 Getting Started

1. **Install Dependencies**: `pip install -r requirements.txt`
2. **Test Live System**: `python demo_3thread.py`
3. **Process Videos**: `python video_processor.py your_video.mp4`
4. **Explore Results**: Check output folders for clips and analysis

The system is designed to be robust, efficient, and easy to use for both development and production deployment.
