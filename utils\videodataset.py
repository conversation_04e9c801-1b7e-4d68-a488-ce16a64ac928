import pandas as pd
import cv2
import torch
from PIL import Image
from torchvision import transforms as tf
from torchvision.transforms import v2
from torch.utils.data import Dataset

import numpy as np

#VideoDataset class for retrieving list of videos for training and testing
class VideoDataset(Dataset):
    def __init__(self, csv_file:str, num_frames:int):
        self.annotations = pd.read_csv(csv_file)
        self.num_frames = num_frames
        self.transform = tf.Compose([tf.Resize((112,112),
                                        interpolation=tf.InterpolationMode.NEAREST_EXACT),
                                        tf.RandomHorizontalFlip(p=0.5),
                                        #tf.RandomVerticalFlip(p=0.3),
                                        #tf.RandomRotation(degrees=10,
                                        #interpolation=tf.InterpolationMode.NEAREST_EXACT,
                                        #fill=0),
                                        #tf.RandomPerspective(distortion_scale=0.05,p=0.3),
                                        tf.RandomAdjustSharpness(sharpness_factor=1.2,p=0.5),
                                        v2.ToDtype(torch.float32,scale=True),
                                        tf.ToTensor(),
                                        tf.Normalize(mean=[0.43216, 0.394666, 0.37645],
                                                     std=[0.22803, 0.22145, 0.216989])])
    
    def __len__(self):
        return len(self.annotations)

    #Method called by Pytorch automatically for loading items from the dataset.
    def __getitem__(self, index):
        vid_path = self.annotations.iloc[index,0]
        label = self.annotations.iloc[index,1]
        frames = self.load_video(vid_path)
        label = float(label)
        return frames, torch.tensor(label)

    #Method for loading videos as tensors.
    def load_video(self, path:str):
        cap = cv2.VideoCapture(path)
        frames = []
        tf_frames = []
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)  # BGR -> RGB
            img = Image.fromarray(frame)
            #Image transformation
            frames.append(img)
        cap.release()
        frames = self.sample_frames(frames, self.num_frames)
        for f in frames:
            tf_frame = self.transform(f)
            tf_frames.append(tf_frame)
        frames = torch.stack(tf_frames)  # (T, C, H, W)
        frames = frames.permute(1, 0, 2, 3)  # (C, T, H, W)

        return frames


    def sample_frames(self, frames:list, num_frames:int):
        total = len(frames)
        if total >= num_frames:
            indices = np.linspace(0, total - 1, num_frames, dtype=int)
            sampled = [frames[i] for i in indices]
        else:
            sampled = frames.copy()
            last_frame = frames[-1]
            pad_frames = [last_frame.copy() for _ in range(num_frames - total)]
            sampled += pad_frames

        return sampled

class ValidDataset(Dataset):
    def __init__(self, csv_file:str, num_frames:int):
        self.annotations = pd.read_csv(csv_file)
        self.num_frames = num_frames
        self.transform = tf.Compose([tf.Resize((112,112),
                                        interpolation=tf.InterpolationMode.NEAREST_EXACT),
                                        v2.ToDtype(torch.float32,scale=True),
                                        tf.ToTensor(),
                                        tf.Normalize(mean=[0.43216, 0.394666, 0.37645],
                                                     std=[0.22803, 0.22145, 0.216989])])
    
    def __len__(self):
        return len(self.annotations)

    #Method called by Pytorch automatically for loading items from the dataset.
    def __getitem__(self, index):
        vid_path = self.annotations.iloc[index,0]
        label = self.annotations.iloc[index,1]
        label = float(label)
        frames = self.load_video(vid_path)
        return frames, torch.tensor(label)

    #Method for loading videos as tensors.
    def load_video(self, path:str):
        cap = cv2.VideoCapture(path)
        frames = []
        tf_frames = []
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)  # BGR -> RGB
            img = Image.fromarray(frame)
            #Image transformation
            frames.append(img)
        cap.release()
        frames = self.sample_frames(frames, self.num_frames)
        for f in frames:
            tf_frame = self.transform(f)
            tf_frames.append(tf_frame)
        frames = torch.stack(tf_frames)  # (T, C, H, W)
        frames = frames.permute(1, 0, 2, 3)  # (C, T, H, W)

        return frames


    def sample_frames(self, frames:list, num_frames:int):
        total = len(frames)
        if total >= num_frames:
            indices = np.linspace(0, total - 1, num_frames, dtype=int)
            sampled = [frames[i] for i in indices]
        else:
            sampled = frames.copy()
            last_frame = frames[-1]
            pad_frames = [last_frame.copy() for _ in range(num_frames - total)]
            sampled += pad_frames

        return sampled

def play_video(path:str):
    player = cv2.VideoCapture(path)
    while player.isOpened():
        ret, frame = player.read()
        if not ret:
            break

        cv2.namedWindow('CCTV',cv2.WINDOW_NORMAL)
        cv2.resizeWindow('CCTV',800,600)
        cv2.imshow('CCTV',frame)
        if cv2.waitKey(40) & 0xFF == ord('q'):
            break
    player.release()


def save_video(path:str, filename:str, bframes:list):
    save_name = f'{path}/{filename}.mp4'
    writer = cv2.VideoWriter(save_name,cv2.VideoWriter_fourcc(*'mp4v'),25,(640,480))
    for f in bframes:
        f = cv2.resize(f,(640,480),interpolation=cv2.INTER_AREA)
        writer.write(f)
    writer.release()


def load_video_tensor(path:str):
    cap = cv2.VideoCapture(path)
    tfs = tf.Compose([tf.Resize((112,112),
                        interpolation=tf.InterpolationMode.NEAREST_EXACT),
                        tf.ToTensor(),
                        v2.ToDtype(torch.float32,scale=True),
                        tf.Normalize(mean=[0.43216, 0.394666, 0.37645],
                        std=[0.22803, 0.22145, 0.216989])])
    frames = []
    tf_frames = []
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)  # BGR -> RGB
        frames.append(frame)
    frames = sample_frames(frames, 16)

    for frame in frames:
        img = Image.fromarray(frame)
        #Image transformation
        tf_frame = tfs(img)
        tf_frames.append(tf_frame)
    cap.release()
        
    frames = torch.stack(tf_frames)  # (T, C, H, W)
    frames = frames.permute(1, 0, 2, 3)  # (C, T, H, W)

    return frames

def sample_frames(frames, num_frames:int):
    total = len(frames)
    if total >= num_frames:
        indices = np.linspace(0, total - 1, num_frames, dtype=int)
        sampled = [frames[i] for i in indices]
    else:
        sampled = frames.copy()
        last_frame = frames[-1]
        pad_frames = [last_frame.copy() for _ in range(num_frames - total)]
        sampled += pad_frames

    return sampled
