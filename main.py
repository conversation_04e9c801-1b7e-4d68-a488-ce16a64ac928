import cv2
import os
import torch
from tempfile import TemporaryDirectory
from collections import defaultdict
import datetime as dt

# Import utilities
from utils.detection import detect_persons
from utils.cropping import crop_person
from utils.prediction import predict_video_clip
from utils.visualization import draw_simple_boxes
from utils.video_processing import split_video_into_clips, save_video_from_frames
from utils.models import initialize_models, get_device

input_path = "data/feed/Untitled Project.mp4"

# Initialize device and models using utils
device = get_device()
model, yolo_model = initialize_models(
    'data/models/trained_model_224.pt',
    'data/models/yolo11s.pt',
    device
)


def person_detector(frame):
    """Detect persons using utils function"""
    device_index = device.index if device.type == 'cuda' else None
    return detect_persons(yolo_model, frame, confidence_threshold=0.1, device_index=device_index)

def predict(video_path):
    """Make prediction using utils function"""
    return predict_video_clip(model, video_path, device, warmup_runs=3)


def draw_boxes(frame, boxes, labels, confidences, ids):
    """Draw bounding boxes using utils function"""
    return draw_simple_boxes(frame, boxes, labels, confidences, ids)


def process_clip_frames(frames, suspicious_flag):
    person_crops = defaultdict(list)
    frame_results = []

    for idx, frame in enumerate(frames):
        boxes, confs, ids = person_detector(frame)
        for pid, box in enumerate(boxes):
            _crop = crop_person(frame, box, target_size=(224, 224), context=0.1)
            person_crops[pid].append(_crop)
        frame_results.append((frame, boxes, confs, ids))

    person_labels = {}
    for pid, crops in person_crops.items():
        if len(crops) < 8:
            continue
        with TemporaryDirectory() as tempdir:
            video_path = os.path.join(tempdir, f"person_{pid}.mp4")
            out = cv2.VideoWriter(video_path, cv2.VideoWriter_fourcc(*'mp4v'), 16, (224, 224))
            for f in crops:
                out.write(f)
            out.release()
            label = predict(video_path)
        person_labels[pid] = label

    annotated_frames = []
    for frame, boxes, confs, ids in frame_results:
        labels = [person_labels.get(pid, 0.0) for pid in range(len(boxes))]
        frame = draw_boxes(frame, boxes, labels, confs, ids)
        annotated_frames.append(frame)

    return annotated_frames


def split_video(video_path, fps=16):
    """Split video using utils function"""
    clips, resolution = split_video_into_clips(video_path, frames_per_clip=fps)
    return clips, resolution


def save_video(all_frames, out_path, fps=16, resolution=(640, 480)):
    """Save video using utils function"""
    save_video_from_frames(all_frames, out_path, fps=fps, resolution=resolution)


def process_video(input_video_path, output_video_path):
    print("Splitting video...")
    clips, resolution = split_video(input_video_path, fps=16)
    all_frames = []
    suspicious_flag = [False]

    print(f"Processing {len(clips)} clips...")
    for i, clip_frames in enumerate(clips):
        print(f"Processing clip {i+1}/{len(clips)}")
        annotated = process_clip_frames(clip_frames, suspicious_flag)
        all_frames.extend(annotated)

    print("Saving final video...")
    save_video(all_frames, output_video_path, fps=25, resolution=resolution)
    print(f"Video saved to: {output_video_path}")

if __name__ == "__main__":
    now = dt.datetime.now()
    now  = now.strftime("%d-%m-%Y_%H-%M-%S")
    output_path = f"data/output/{now}.mp4"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    process_video(input_path, output_path)

