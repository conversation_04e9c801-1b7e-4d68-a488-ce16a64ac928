import torch
import torch.nn.functional as F
import math
from torch import nn, device
from torchvision import ops
from torchvision.models.video import mc3_18, MC3_18_Weights
from einops import rearrange
from torch.utils.data import Dataset


#Action transformer architecture

class PositionalEncoding(nn.Module):
    def __init__(self,d_model=512,max_len=7000):
        #Positional encoding formula:
            #sin,cos(t/N^(k/d_model))
        super().__init__()
        pe = torch.zeros(max_len,d_model)
        pos = torch.arange(0,max_len,dtype=torch.float32).unsqueeze(1)
        div_term = torch.exp(torch.arange(0,d_model,2,dtype=torch.float32)
                             *(-math.log(10000)/d_model))
        pe[:, 0::2] = torch.sin(pos * div_term)
        pe[:, 1::2] = torch.cos(pos * div_term)
        pe = pe.unsqueeze(0)  # Shape: [1, max_len, d_model]
        self.register_buffer('pe', pe)

    def forward(self, x):
        # x shape: [B, S, C]
        seq_len = x.size(1)
        return x + self.pe[:, :seq_len]


class ActionTransformer(nn.Module):
    def __init__(self):
        super().__init__()
        self.device = device('cuda:0')

        #Backbone of the action transformer. Uses a pretrained 3D CNN.
        self.backbone = mc3_18(weights=MC3_18_Weights.KINETICS400_V1)

        # Extract only the convolutional feature extractor
        self.backbone.avgpool = nn.Identity()
        self.backbone.fc = nn.Identity()
        

        #Positional Encoder
        self.pos_encoder = PositionalEncoding(d_model=512).to(self.device)

        #Transformer Encoder
        encoder_layer = nn.TransformerEncoderLayer(d_model=512, 
                                                   nhead=16,
                                                   dropout=0.25,
                                                   activation='gelu', 
                                                   batch_first=True)
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=12)\
                                                .to(self.device)

        #MLP classification head
        self.mlp = ops.MLP(in_channels=512, hidden_channels=[2048,512],
                           dropout=0.25,activation_layer=nn.ReLU).to(self.device)
        
        self.linear1 =  nn.Linear(512,64).to(self.device)
        self.linear2 = nn.Linear(64,1).to(self.device)

    def forward(self, x):
        features = self.backbone.stem(x)
        features = self.backbone.layer1(features)
        features = self.backbone.layer2(features)
        features = self.backbone.layer3(features)
        features = self.backbone.layer4(features)
        #features = features.unsqueeze(-1).unsqueeze(-1).unsqueeze(-1)
        features = F.adaptive_avg_pool3d(features,(features.shape[2],1,1))
        #Rearrange to (B, sequence_length, C)
        features = features.squeeze(-1).squeeze(-1)
        features = rearrange(features, 'b c t -> b t c')  # [B, S, 512]
        #Positional encoding
        features = self.pos_encoder(features)
        #Transformer
        features = self.transformer(features)
        features = features.mean(dim=1)
    
        features = self.mlp(features)
        features = self.linear1(features)
        return self.linear2(features)
