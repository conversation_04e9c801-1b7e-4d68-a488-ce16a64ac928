"""
Cropping utilities for person extraction and processing
"""

import cv2
import numpy as np
from typing import Tuple, List


def crop_person(frame: np.ndarray, box: Tuple[int, int, int, int], 
                target_size: Tuple[int, int] = (224, 224), context: float = 0.1) -> np.ndarray:
    """
    Crop person from frame and resize to target size with square aspect ratio
    
    Args:
        frame: Input frame
        box: Bounding box (x1, y1, x2, y2)
        target_size: Target size for output crop (width, height)
        context: Additional context around bounding box (0.1 = 10% padding)
    
    Returns:
        Cropped and resized frame
    """
    h, w = frame.shape[:2]
    x1, y1, x2, y2 = box
    
    # Calculate box dimensions
    box_w, box_h = x2 - x1, y2 - y1
    context_size = int(max(box_w, box_h) * context)
    
    # Add context padding
    x1 = max(x1 - context_size, 0)
    y1 = max(y1 - context_size, 0)
    x2 = min(x2 + context_size, w)
    y2 = min(y2 + context_size, h)
    
    # Make crop square by adjusting the smaller dimension
    crop_w, crop_h = x2 - x1, y2 - y1
    diff = abs(crop_w - crop_h)
    
    if crop_w > crop_h:
        # Crop is wider, extend height
        y1 = max(y1 - diff // 2, 0)
        y2 = min(y2 + diff - diff // 2, h)
    else:
        # Crop is taller, extend width
        x1 = max(x1 - diff // 2, 0)
        x2 = min(x2 + diff - diff // 2, w)
    
    # Extract crop
    square_crop = frame[y1:y2, x1:x2]
    
    # Handle empty crops
    if square_crop.size == 0:
        square_crop = np.zeros((target_size[1], target_size[0], 3), dtype=np.uint8)
    else:
        # Resize to target size
        square_crop = cv2.resize(square_crop, target_size)
    
    return square_crop


def crop_person_simple(frame: np.ndarray, box: Tuple[int, int, int, int], 
                      target_size: Tuple[int, int] = (224, 224)) -> np.ndarray:
    """
    Simple person cropping with boundary handling
    
    Args:
        frame: Input frame
        box: Bounding box (x1, y1, x2, y2)
        target_size: Target size for output crop (width, height)
    
    Returns:
        Cropped and resized frame
    """
    x1, y1, x2, y2 = box
    h, w = frame.shape[:2]
    
    # Ensure coordinates are within frame bounds
    x1, y1 = max(0, x1), max(0, y1)
    x2, y2 = min(w, x2), min(h, y2)
    
    # Extract crop
    crop = frame[y1:y2, x1:x2]
    
    # Handle invalid crops
    if crop.size == 0:
        crop = np.zeros((target_size[1], target_size[0], 3), dtype=np.uint8)
    else:
        crop = cv2.resize(crop, target_size)
    
    return crop


def save_person_clip(crops: List[np.ndarray], output_path: str, fps: float = 16.0) -> bool:
    """
    Save list of cropped frames as a video clip
    
    Args:
        crops: List of cropped frames
        output_path: Output video file path
        fps: Frames per second for output video
    
    Returns:
        True if successful, False otherwise
    """
    if not crops:
        return False
    
    try:
        # Get dimensions from first frame
        h, w = crops[0].shape[:2]
        
        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (w, h))
        
        # Write frames
        for crop in crops:
            out.write(crop)
        
        out.release()
        return True
        
    except Exception as e:
        print(f"Error saving person clip to {output_path}: {e}")
        return False


def extract_person_crops(frames: List[np.ndarray], boxes_per_frame: List[List[Tuple]], 
                        target_size: Tuple[int, int] = (224, 224)) -> dict:
    """
    Extract person crops from multiple frames
    
    Args:
        frames: List of frames
        boxes_per_frame: List of bounding boxes for each frame
        target_size: Target size for crops
    
    Returns:
        Dictionary mapping person_id to list of crops
    """
    person_crops = {}
    
    for frame_idx, (frame, boxes) in enumerate(zip(frames, boxes_per_frame)):
        for person_id, box in enumerate(boxes):
            if person_id not in person_crops:
                person_crops[person_id] = []
            
            crop = crop_person_simple(frame, box, target_size)
            person_crops[person_id].append(crop)
    
    return person_crops
