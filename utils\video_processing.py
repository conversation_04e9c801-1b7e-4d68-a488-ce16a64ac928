"""
Video processing utilities for splitting, saving, and managing video clips
"""

import cv2
import os
import numpy as np
from typing import List, Tuple, Optional
import datetime as dt


def split_video_into_clips(video_path: str, frames_per_clip: int = 16) -> Tuple[List[List[np.ndarray]], Tuple[int, int]]:
    """
    Split video into clips of specified frame count
    
    Args:
        video_path: Path to input video
        frames_per_clip: Number of frames per clip
    
    Returns:
        Tuple of (clips_list, (width, height))
    """
    cap = cv2.VideoCapture(video_path)
    clips, current_clip = [], []
    width, height = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)), int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            # Add remaining frames as final clip if any
            if current_clip:
                clips.append(current_clip)
            break
        
        current_clip.append(frame)
        frame_count += 1
        
        # Create new clip when reaching target frame count
        if frame_count % frames_per_clip == 0:
            clips.append(current_clip)
            current_clip = []
    
    cap.release()
    return clips, (width, height)


def save_video_from_frames(frames: List[np.ndarray], output_path: str, 
                          fps: float = 16.0, resolution: Optional[Tuple[int, int]] = None) -> bool:
    """
    Save frames as video file
    
    Args:
        frames: List of frames to save
        output_path: Output video file path
        fps: Frames per second
        resolution: Optional resolution (width, height). If None, uses frame size
    
    Returns:
        True if successful, False otherwise
    """
    if not frames:
        return False
    
    try:
        # Determine resolution
        if resolution is None:
            h, w = frames[0].shape[:2]
            resolution = (w, h)
        
        # Create output directory if needed
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, resolution)
        
        # Write frames
        for frame in frames:
            # Resize frame if needed
            if frame.shape[:2][::-1] != resolution:
                frame = cv2.resize(frame, resolution)
            out.write(frame)
        
        out.release()
        return True
        
    except Exception as e:
        print(f"Error saving video to {output_path}: {e}")
        return False


def create_timestamped_filename(base_name: str, extension: str = ".mp4", 
                               timestamp_format: str = "%Y%m%d_%H%M%S_%f") -> str:
    """
    Create timestamped filename
    
    Args:
        base_name: Base name for file
        extension: File extension
        timestamp_format: Timestamp format string
    
    Returns:
        Timestamped filename
    """
    timestamp = dt.datetime.now().strftime(timestamp_format)[:-3]  # Remove last 3 digits of microseconds
    return f"{base_name}_{timestamp}{extension}"


def create_session_directory(base_dir: str, session_format: str = "%d-%m-%Y_%H-%M-%S") -> str:
    """
    Create timestamped session directory
    
    Args:
        base_dir: Base directory path
        session_format: Session timestamp format
    
    Returns:
        Path to created session directory
    """
    session_timestamp = dt.datetime.now().strftime(session_format)
    session_dir = os.path.join(base_dir, session_timestamp)
    
    # Create directory structure
    os.makedirs(session_dir, exist_ok=True)
    os.makedirs(os.path.join(session_dir, 'feed'), exist_ok=True)
    os.makedirs(os.path.join(session_dir, 'annotated'), exist_ok=True)
    
    return session_dir


def get_video_info(video_path: str) -> dict:
    """
    Get video file information
    
    Args:
        video_path: Path to video file
    
    Returns:
        Dictionary with video information
    """
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        return {}
    
    info = {
        'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
        'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
        'fps': cap.get(cv2.CAP_PROP_FPS),
        'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
        'duration': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) / cap.get(cv2.CAP_PROP_FPS)
    }
    
    cap.release()
    return info


def extract_frames_from_video(video_path: str, max_frames: Optional[int] = None) -> List[np.ndarray]:
    """
    Extract all frames from video
    
    Args:
        video_path: Path to video file
        max_frames: Maximum number of frames to extract (None for all)
    
    Returns:
        List of frames
    """
    cap = cv2.VideoCapture(video_path)
    frames = []
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frames.append(frame)
        frame_count += 1
        
        if max_frames and frame_count >= max_frames:
            break
    
    cap.release()
    return frames


def resize_video_frames(frames: List[np.ndarray], target_size: Tuple[int, int]) -> List[np.ndarray]:
    """
    Resize all frames to target size
    
    Args:
        frames: List of input frames
        target_size: Target size (width, height)
    
    Returns:
        List of resized frames
    """
    resized_frames = []
    
    for frame in frames:
        resized_frame = cv2.resize(frame, target_size)
        resized_frames.append(resized_frame)
    
    return resized_frames


def validate_video_file(video_path: str) -> bool:
    """
    Validate if video file can be opened and read
    
    Args:
        video_path: Path to video file
    
    Returns:
        True if valid, False otherwise
    """
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return False
        
        # Try to read first frame
        ret, frame = cap.read()
        cap.release()
        
        return ret and frame is not None
        
    except Exception:
        return False
