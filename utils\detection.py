"""
Detection utilities for YOLO person detection and tracking
"""

import cv2
import numpy as np
from typing import List, Tuple


def detect_persons(yolo_model, frame, confidence_threshold=0.1, device_index=None):
    """
    Detect persons in frame using YOLO model
    
    Args:
        yolo_model: YOLO model instance
        frame: Input frame
        confidence_threshold: Minimum confidence for detection
        device_index: GPU device index (None for CPU)
    
    Returns:
        Tuple of (boxes, confidences, ids)
    """
    results = yolo_model.track(
        frame, 
        persist=True, 
        classes=[0],  # Person class
        verbose=False, 
        device=device_index
    )[0]
    
    boxes, confidences, ids = [], [], []
    
    if results.boxes is not None:
        for det in results.boxes:
            conf = float(det.conf)
            track_id = det.id
            
            if conf > confidence_threshold and track_id is not None:
                x1, y1, x2, y2 = map(int, det.xyxy[0])
                boxes.append((x1, y1, x2, y2))
                confidences.append(conf)
                ids.append(int(track_id))
    
    return boxes, confidences, ids


def reset_yolo_tracker(yolo_model):
    """
    Reset YOLO tracker to start fresh tracking
    
    Args:
        yolo_model: YOLO model instance
    """
    try:
        if hasattr(yolo_model, 'predictor') and yolo_model.predictor is not None:
            if hasattr(yolo_model.predictor, 'trackers') and len(yolo_model.predictor.trackers) > 0:
                yolo_model.predictor.trackers[0].reset()
                print("🔄 YOLO tracker reset")
                return True
    except Exception as e:
        print(f"Warning: Could not reset YOLO tracker: {e}")
    return False


def get_tracker_info(yolo_model):
    """
    Get information about the current YOLO tracker state
    
    Args:
        yolo_model: YOLO model instance
    
    Returns:
        String describing tracker state
    """
    try:
        if hasattr(yolo_model, 'predictor') and yolo_model.predictor is not None:
            if hasattr(yolo_model.predictor, 'trackers') and len(yolo_model.predictor.trackers) > 0:
                tracker = yolo_model.predictor.trackers[0]
                if hasattr(tracker, 'tracks'):
                    active_tracks = len([t for t in tracker.tracks if t.is_confirmed()])
                    return f"Active tracks: {active_tracks}"
        return "Tracker not initialized"
    except Exception as e:
        return f"Tracker error: {e}"


def initialize_yolo_tracker(yolo_model, frame_shape=(480, 640, 3)):
    """
    Initialize YOLO tracker with a dummy frame
    
    Args:
        yolo_model: YOLO model instance
        frame_shape: Shape of dummy frame (height, width, channels)
    """
    dummy_frame = np.zeros(frame_shape, dtype=np.uint8)
    _ = yolo_model.track(dummy_frame, persist=True, classes=[0], verbose=False)
    reset_yolo_tracker(yolo_model)
