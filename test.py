#!/usr/bin/env python3
"""
Video Testing Script with Predictions and Annotations

This script loads video files from a CSV using pandas, uses the ValidDataset class
from videodataset.py and PyTorch DataLoader to make predictions on videos,
and plays the videos in an OpenCV window with prediction annotations.

QUICK START:
1. Set CSV_FILE_PATH and MODEL_PATH below (lines ~35-40)
2. Run: python test.py
3. Watch videos with real-time predictions!

Usage:
    python test.py                    # Use paths configured in script
    python test.py --cli              # Use command line arguments
"""

import torch
import torch.nn as nn
import cv2
import pandas as pd
import numpy as np
import argparse
import os
import time
from pathlib import Path
from torch.utils.data import DataLoader

# Import custom modules
import utils.actiontransformer as act
from utils.videodataset import ValidDataset

# ============================================================================
# CONFIGURATION - SET YOUR PATHS HERE
# ============================================================================

# Set the path to your CSV file with video paths and labels
CSV_FILE_PATH = "data/MNNIT/all/test_annot.csv"  # ← Change this to your CSV file

# Set the path to your trained model
MODEL_PATH = "data/models/trained_model_224.pt"  # ← Change this to your model

# Testing parameters
BATCH_SIZE = 1      # Batch size for processing (1 recommended for interactive testing)
NUM_FRAMES = 16      # Number of frames to sample from each video
AUTO_CONTINUE = True  # Set to True to automatically continue between videos

# ============================================================================

class VideoTester:
    """
    Class for testing videos with ActionTransformer model and displaying results
    """

    def __init__(self, csv_file: str, model_path: str, batch_size: int = 1, num_frames: int = 8):
        """
        Initialize the video tester

        Args:
            csv_file: Path to CSV file containing video paths and labels
            model_path: Path to trained ActionTransformer model
            batch_size: Batch size for DataLoader
            num_frames: Number of frames to sample from each video
        """
        self.csv_file = csv_file
        self.model_path = model_path
        self.batch_size = batch_size
        self.num_frames = num_frames
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.auto_continue = False  # Can be set to True for automated testing

        print(f"🔧 Initializing Video Tester")
        print(f"   📁 CSV file: {csv_file}")
        print(f"   🤖 Model: {model_path}")
        print(f"   🖥️  Device: {self.device}")
        print(f"   📊 Batch size: {batch_size}")
        print(f"   🎬 Frames per video: {num_frames}")

        # Load model
        self.model = self._load_model()

        # Create dataset and dataloader
        self.dataset = ValidDataset(csv_file, num_frames)
        self.dataloader = DataLoader(
            self.dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=0  # Set to 0 to avoid multiprocessing issues with OpenCV
        )

        print(f"✅ Loaded {len(self.dataset)} videos from CSV")

    def _load_model(self):
        """Load the trained ActionTransformer model"""
        print(f"📥 Loading model from {self.model_path}")

        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"Model file not found: {self.model_path}")

        # Initialize model
        model = act.ActionTransformer()

        # Load checkpoint
        checkpoint = torch.load(self.model_path, map_location=self.device)
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(self.device)
        model.eval()

        print(f"✅ Model loaded successfully")
        print(f"   📊 Epoch: {checkpoint.get('epoch', 'N/A')}")
        print(f"   📊 Val Loss: {checkpoint.get('sval_loss', 'N/A')}")
        print(f"   📊 Val Accuracy: {checkpoint.get('val_accuracy', 'N/A')}")

        return model

    def predict_batch(self, frames_batch):
        """
        Make predictions on a batch of video frames

        Args:
            frames_batch: Tensor of shape [batch_size, channels, time, height, width]

        Returns:
            predictions: List of prediction probabilities
        """
        with torch.no_grad():
            frames_batch = frames_batch.to(self.device)
            outputs = self.model(frames_batch)
            probabilities = torch.sigmoid(outputs).cpu().numpy()
            return probabilities.flatten()

    def load_video_for_display(self, video_path: str):
        """
        Load video frames for display (original resolution)

        Args:
            video_path: Path to video file

        Returns:
            frames: List of frames in BGR format for OpenCV display
        """
        cap = cv2.VideoCapture(video_path)
        frames = []

        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)

        cap.release()
        return frames

    def annotate_frame(self, frame, prediction_prob: float, true_label: int, frame_idx: int, total_frames: int):
        """
        Annotate frame with prediction results

        Args:
            frame: OpenCV frame (BGR format)
            prediction_prob: Prediction probability (0-1)
            true_label: True label (0 or 1)
            frame_idx: Current frame index
            total_frames: Total number of frames

        Returns:
            annotated_frame: Frame with annotations
        """
        frame = frame.copy()
        height, width = frame.shape[:2]

        # Determine prediction
        is_suspicious = prediction_prob > 0.5

        # Colors
        color_suspicious = (0, 0, 255)  # Red
        color_normal = (0, 255, 0)      # Green
        color_text = (255, 255, 255)    # White

        # Choose color based on prediction
        pred_color = color_suspicious if is_suspicious else color_normal

        # Draw prediction box
        cv2.rectangle(frame, (10, 10), (width - 10, 120), pred_color, 3)

        # Add prediction text
        pred_text = "SUSPICIOUS" if is_suspicious else "NORMAL"
        cv2.putText(frame, f"Prediction: {pred_text}", (20, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, color_text, 2)

        cv2.putText(frame, f"Confidence: {prediction_prob:.3f}", (20, 70),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color_text, 2)

        # Add true label
        true_text = "SUSPICIOUS" if true_label == 1 else "NORMAL"
        true_color = color_suspicious if true_label == 1 else color_normal
        cv2.putText(frame, f"True Label: {true_text}", (20, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, true_color, 2)

        # Add frame counter
        cv2.putText(frame, f"Frame: {frame_idx + 1}/{total_frames}", (width - 200, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color_text, 2)

        # Add accuracy indicator
        is_correct = (is_suspicious and true_label == 1) or (not is_suspicious and true_label == 0)
        accuracy_text = "CORRECT" if is_correct else "INCORRECT"
        accuracy_color = (0, 255, 0) if is_correct else (0, 0, 255)
        cv2.putText(frame, accuracy_text, (width - 150, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, accuracy_color, 2)

        return frame

    def play_video_with_predictions(self, video_path: str, prediction_prob: float, true_label: int):
        """
        Play video with prediction annotations

        Args:
            video_path: Path to video file
            prediction_prob: Prediction probability
            true_label: True label
        """
        print(f"🎬 Playing video: {os.path.basename(video_path)}")
        print(f"   🎯 Prediction: {prediction_prob:.3f} ({'Suspicious' if prediction_prob > 0.5 else 'Normal'})")
        print(f"   📋 True Label: {'Suspicious' if true_label == 1 else 'Normal'}")

        # Load video frames
        frames = self.load_video_for_display(video_path)

        if not frames:
            print(f"❌ Could not load video: {video_path}")
            return

        # Create window
        cv2.namedWindow('Video Prediction Test', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Video Prediction Test', 800, 600)

        # Play video
        for i, frame in enumerate(frames):
            # Annotate frame
            annotated_frame = self.annotate_frame(frame, prediction_prob, true_label, i, len(frames))

            # Display frame
            cv2.imshow('Video Prediction Test', annotated_frame)

            # Control playback speed (25 FPS = 40ms delay)
            key = cv2.waitKey(40) & 0xFF

            if key == ord('q'):
                print("⏹️  Playback stopped by user")
                break
            elif key == ord(' '):  # Spacebar to pause
                print("⏸️  Paused - Press any key to continue")
                cv2.waitKey(0)
            elif key == ord('s'):  # 's' to skip to next video
                print("⏭️  Skipping to next video")
                break

        cv2.destroyAllWindows()

    def test_all_videos(self):
        """
        Test all videos in the dataset and display results
        """
        print(f"\n🎯 Starting video testing with predictions")
        print(f"📋 Controls:")
        print(f"   - Press 'q' to quit")
        print(f"   - Press 'space' to pause/resume")
        print(f"   - Press 's' to skip to next video")
        print(f"   - Close window or press 'q' to exit")
        print()

        correct_predictions = 0
        total_predictions = 0

        for batch_idx, (frames_batch, labels_batch) in enumerate(self.dataloader):
            # Make predictions
            predictions = self.predict_batch(frames_batch)

            # Process each video in the batch
            for i in range(len(predictions)):
                video_idx = batch_idx * self.batch_size + i
                video_path = self.dataset.annotations.iloc[video_idx, 0]
                true_label = int(labels_batch[i].item())
                prediction_prob = predictions[i]

                # Check accuracy
                is_correct = (prediction_prob > 0.5 and true_label == 1) or (prediction_prob <= 0.5 and true_label == 0)
                if is_correct:
                    correct_predictions += 1
                total_predictions += 1

                print(f"\n📹 Video {video_idx + 1}/{len(self.dataset)}: {os.path.basename(video_path)}")

                # Play video with annotations
                self.play_video_with_predictions(video_path, prediction_prob, true_label)

                # Ask user if they want to continue (unless auto-continue is enabled)
                print(f"📊 Current Accuracy: {correct_predictions}/{total_predictions} ({correct_predictions/total_predictions*100:.1f}%)")

                if self.auto_continue:
                    print("🔄 Auto-continue enabled - proceeding to next video...")
                    user_input = 'y'
                else:
                    user_input = input("Continue to next video? (y/n/q): ").lower().strip()

                if user_input in ['n', 'q', 'quit', 'exit']:
                    print("🛑 Testing stopped by user")
                    break

            if user_input in ['n', 'q', 'quit', 'exit']:
                break

        # Final results
        print(f"\n📊 FINAL RESULTS:")
        print(f"   ✅ Correct Predictions: {correct_predictions}")
        print(f"   📊 Total Predictions: {total_predictions}")
        print(f"   🎯 Accuracy: {correct_predictions/total_predictions*100:.2f}%")

def run_test_from_config():
    """Run video testing using the configuration at the top of the file"""
    print("🎯 Video Testing with ActionTransformer")
    print("=" * 60)
    print("📋 Using configuration from script:")
    print(f"   📁 CSV file: {CSV_FILE_PATH}")
    print(f"   🤖 Model: {MODEL_PATH}")
    print(f"   📊 Batch size: {BATCH_SIZE}")
    print(f"   🎬 Frames per video: {NUM_FRAMES}")
    print(f"   🔄 Auto continue: {AUTO_CONTINUE}")
    print()

    # Validate inputs
    if not os.path.exists(CSV_FILE_PATH):
        print(f"❌ CSV file not found: {CSV_FILE_PATH}")
        print("💡 Please check the CSV_FILE_PATH in the script configuration")
        print("💡 You can use the sample file: sample_test_videos.csv")
        return False

    if not os.path.exists(MODEL_PATH):
        print(f"❌ Model file not found: {MODEL_PATH}")
        print("💡 Please check the MODEL_PATH in the script configuration")
        return False

    try:
        # Create tester
        tester = VideoTester(
            csv_file=CSV_FILE_PATH,
            model_path=MODEL_PATH,
            batch_size=BATCH_SIZE,
            num_frames=NUM_FRAMES
        )

        # Modify tester for auto-continue if enabled
        if AUTO_CONTINUE:
            print("🔄 Auto-continue mode enabled - videos will play automatically")
            tester.auto_continue = True

        # Run tests
        tester.test_all_videos()

        print(f"\n🎉 Video testing completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Video testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function - supports both config mode and CLI mode"""
    parser = argparse.ArgumentParser(
        description="Test videos with ActionTransformer predictions",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test.py                    # Use config at top of file
  python test.py --cli              # Use command line mode
  python test.py --cli --csv data/test.csv --model model.pt
        """
    )

    parser.add_argument("--cli", action="store_true",
                       help="Use command line interface instead of config")
    parser.add_argument("--csv", type=str,
                       help="Path to CSV file with video paths and labels (CLI mode)")
    parser.add_argument("--model", type=str,
                       help="Path to trained ActionTransformer model (CLI mode)")
    parser.add_argument("--batch-size", type=int, default=1,
                       help="Batch size for processing (CLI mode, default: 1)")
    parser.add_argument("--num-frames", type=int, default=8,
                       help="Number of frames to sample from each video (CLI mode, default: 8)")

    args = parser.parse_args()

    # CLI mode
    if args.cli:
        if not args.csv or not args.model:
            print("❌ --csv and --model are required in CLI mode")
            parser.print_help()
            return

        if not os.path.exists(args.csv):
            print(f"❌ CSV file not found: {args.csv}")
            return

        if not os.path.exists(args.model):
            print(f"❌ Model file not found: {args.model}")
            return

        try:
            # Create tester and run tests
            tester = VideoTester(
                csv_file=args.csv,
                model_path=args.model,
                batch_size=args.batch_size,
                num_frames=args.num_frames
            )

            tester.test_all_videos()

        except KeyboardInterrupt:
            print(f"\n⏹️  Testing interrupted by user")
        except Exception as e:
            print(f"❌ Error during testing: {e}")
            import traceback
            traceback.print_exc()

    # Config mode (default)
    else:
        try:
            success = run_test_from_config()
            if success:
                print(f"\n💡 Next steps:")
                print(f"   1. Modify CSV_FILE_PATH and MODEL_PATH for different tests")
                print(f"   2. Set AUTO_CONTINUE=True for automated testing")
                print(f"   3. Use --cli mode for command line control")
            else:
                print(f"\n💡 Troubleshooting:")
                print(f"   - Check that CSV_FILE_PATH points to a valid CSV file")
                print(f"   - Ensure MODEL_PATH points to your trained model")
                print(f"   - Try using --cli mode for more control")
                print(f"   - Use sample_test_videos.csv for initial testing")
        except KeyboardInterrupt:
            print(f"\n⏹️  Testing interrupted by user")

if __name__ == "__main__":
    main()