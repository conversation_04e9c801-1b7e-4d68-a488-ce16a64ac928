#!/usr/bin/env python3
"""
Example usage of Multi-Video Processor
Demonstrates different configurations and use cases
"""

from multi_video_processor import MultiVideoProcessor
import os

def example_basic_usage():
    """Basic usage example"""
    print("🎬 Basic Multi-Video Processing Example")
    print("=" * 50)
    
    # Set your video folder path
    video_folder = "data/feed"  # Change this to your video folder
    
    # Check if folder exists
    if not os.path.exists(video_folder):
        print(f"❌ Video folder not found: {video_folder}")
        print("Please update the video_folder path in this script")
        return
    
    try:
        # Create processor with default settings (4 videos max)
        processor = MultiVideoProcessor(video_folder, max_videos=4)
        
        # Process videos and display grid
        processor.process_videos()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def example_small_grid():
    """Example with 2 videos in a 1x2 grid"""
    print("🎬 Small Grid Example (2 videos)")
    print("=" * 50)
    
    video_folder = "data/feed"
    
    if not os.path.exists(video_folder):
        print(f"❌ Video folder not found: {video_folder}")
        return
    
    try:
        # Process only 2 videos for a smaller grid
        processor = MultiVideoProcessor(video_folder, max_videos=2)
        processor.process_videos()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def example_large_grid():
    """Example with 6 videos in a 3x2 grid"""
    print("🎬 Large Grid Example (6 videos)")
    print("=" * 50)
    
    video_folder = "data/feed"
    
    if not os.path.exists(video_folder):
        print(f"❌ Video folder not found: {video_folder}")
        return
    
    try:
        # Process 6 videos for a larger grid
        processor = MultiVideoProcessor(video_folder, max_videos=6)
        processor.process_videos()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def list_available_videos():
    """List all available videos in the folder"""
    print("📁 Available Videos")
    print("=" * 50)
    
    video_folder = "data/feed"
    
    if not os.path.exists(video_folder):
        print(f"❌ Video folder not found: {video_folder}")
        return
    
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
    video_files = []
    
    for file in os.listdir(video_folder):
        file_path = os.path.join(video_folder, file)
        if os.path.isfile(file_path):
            if any(file.lower().endswith(ext) for ext in video_extensions):
                video_files.append(file)
    
    if video_files:
        print(f"Found {len(video_files)} video files:")
        for i, video in enumerate(sorted(video_files), 1):
            print(f"  {i}. {video}")
    else:
        print("No video files found in the folder")
    
    print(f"\nFolder path: {os.path.abspath(video_folder)}")

def main():
    """Main function with menu"""
    print("🎬 Multi-Video Processor Examples")
    print("=" * 50)
    print("Choose an example to run:")
    print("1. Basic usage (4 videos)")
    print("2. Small grid (2 videos)")
    print("3. Large grid (6 videos)")
    print("4. List available videos")
    print("5. Exit")
    
    while True:
        try:
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == '1':
                example_basic_usage()
                break
            elif choice == '2':
                example_small_grid()
                break
            elif choice == '3':
                example_large_grid()
                break
            elif choice == '4':
                list_available_videos()
                # Don't break, allow user to choose another option
            elif choice == '5':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1-5.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
