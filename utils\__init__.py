"""
Utils package for Live Shoplifting Detection System

This package contains utility modules for:
- detection: YOLO person detection and tracking
- cropping: Person cropping and extraction
- prediction: ActionTransformer model predictions
- visualization: Bounding box drawing and annotations
- video_processing: Video splitting, saving, and management
- models: Model loading and initialization
- actiontransformer: ActionTransformer model architecture
- videodataset: Video dataset classes and utilities
"""

# Import commonly used functions for easy access
from .detection import detect_persons, reset_yolo_tracker, get_tracker_info, initialize_yolo_tracker
from .cropping import crop_person, crop_person_simple, save_person_clip, extract_person_crops
from .prediction import predict_video_clip, predict_with_timing, get_prediction_label, initialize_model
from .visualization import draw_bounding_boxes, draw_simple_boxes, add_timestamp_overlay, save_annotated_video
from .video_processing import split_video_into_clips, save_video_from_frames, create_timestamped_filename, create_session_directory
from .models import load_yolo_model, load_action_transformer, initialize_models, get_device, validate_models
from .actiontransformer import ActionTransformer, PositionalEncoding
from .videodataset import VideoDataset, ValidDataset, load_video_tensor, sample_frames, play_video, save_video

__all__ = [
    # Detection utilities
    'detect_persons', 'reset_yolo_tracker', 'get_tracker_info', 'initialize_yolo_tracker',

    # Cropping utilities
    'crop_person', 'crop_person_simple', 'save_person_clip', 'extract_person_crops',

    # Prediction utilities
    'predict_video_clip', 'predict_with_timing', 'get_prediction_label', 'initialize_model',

    # Visualization utilities
    'draw_bounding_boxes', 'draw_simple_boxes', 'add_timestamp_overlay', 'save_annotated_video',

    # Video processing utilities
    'split_video_into_clips', 'save_video_from_frames', 'create_timestamped_filename', 'create_session_directory',

    # Model utilities
    'load_yolo_model', 'load_action_transformer', 'initialize_models', 'get_device', 'validate_models',

    # Model architectures
    'ActionTransformer', 'PositionalEncoding',

    # Dataset utilities
    'VideoDataset', 'ValidDataset', 'load_video_tensor', 'sample_frames', 'play_video', 'save_video'
]
