#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to combine all annotated clips from a live session into a single video
Saves the combined video to the parent folder of the session
"""

import cv2
import os
import glob
import argparse
from pathlib import Path
from typing import List, Optional, Tuple
import datetime as dt

# Import utilities
from utils.video_processing import get_video_info, validate_video_file, save_video_from_frames, extract_frames_from_video


def find_session_directories(base_output_dir: str = "data/output") -> List[str]:
    """
    Find all session directories in the output folder
    
    Args:
        base_output_dir: Base output directory to search
    
    Returns:
        List of session directory paths
    """
    session_dirs = []
    
    if not os.path.exists(base_output_dir):
        return session_dirs
    
    # Look for timestamped session folders (dd-mm-yyyy_h-m-s format)
    for item in os.listdir(base_output_dir):
        item_path = os.path.join(base_output_dir, item)
        if os.path.isdir(item_path):
            # Check if it has annotated subfolder
            annotated_dir = os.path.join(item_path, "annotated")
            if os.path.exists(annotated_dir):
                session_dirs.append(item_path)
    
    return sorted(session_dirs)


def get_annotated_clips(session_dir: str) -> List[str]:
    """
    Get all annotated clip files from a session directory
    
    Args:
        session_dir: Path to session directory
    
    Returns:
        List of annotated clip file paths, sorted by timestamp
    """
    annotated_dir = os.path.join(session_dir, "annotated")
    
    if not os.path.exists(annotated_dir):
        return []
    
    # Find all annotated clips
    clip_pattern = os.path.join(annotated_dir, "annotated_*.mp4")
    clips = glob.glob(clip_pattern)
    
    # Sort by filename (which contains timestamp)
    clips.sort()
    
    # Validate clips
    valid_clips = []
    for clip in clips:
        if validate_video_file(clip):
            valid_clips.append(clip)
        else:
            print(f"⚠️  Skipping invalid clip: {clip}")
    
    return valid_clips


def combine_clips(clip_paths: List[str], output_path: str, fps: float = 25.0) -> bool:
    """
    Combine multiple video clips into a single video
    
    Args:
        clip_paths: List of video clip file paths
        output_path: Output video file path
        fps: Output video frame rate
    
    Returns:
        True if successful, False otherwise
    """
    if not clip_paths:
        print("❌ No clips to combine")
        return False
    
    print(f"🎬 Combining {len(clip_paths)} clips...")
    
    try:
        # Get video info from first clip to determine resolution
        first_clip_info = get_video_info(clip_paths[0])
        if not first_clip_info:
            print(f"❌ Could not get info from first clip: {clip_paths[0]}")
            return False
        
        target_width = first_clip_info['width']
        target_height = first_clip_info['height']
        target_resolution = (target_width, target_height)
        
        print(f"📐 Target resolution: {target_width}x{target_height}")
        
        # Create output directory if needed
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, target_resolution)
        
        total_frames = 0
        
        # Process each clip
        for i, clip_path in enumerate(clip_paths):
            print(f"📹 Processing clip {i+1}/{len(clip_paths)}: {os.path.basename(clip_path)}")
            
            # Extract frames from clip
            frames = extract_frames_from_video(clip_path)
            
            if not frames:
                print(f"⚠️  No frames extracted from {clip_path}")
                continue
            
            # Write frames to output video
            for frame in frames:
                # Resize frame if needed
                if frame.shape[:2][::-1] != target_resolution:
                    frame = cv2.resize(frame, target_resolution)
                
                out.write(frame)
                total_frames += 1
        
        out.release()
        
        print(f"✅ Combined video saved: {output_path}")
        print(f"📊 Total frames: {total_frames}")
        print(f"⏱️  Duration: {total_frames / fps:.1f} seconds")
        
        return True
        
    except Exception as e:
        print(f"❌ Error combining clips: {e}")
        return False


def create_combined_filename(session_dir: str) -> str:
    """
    Create filename for combined video based on session directory
    
    Args:
        session_dir: Path to session directory
    
    Returns:
        Filename for combined video
    """
    session_name = os.path.basename(session_dir)
    return f"combined_session_{session_name}.mp4"


def combine_session_clips(session_dir: str, output_dir: Optional[str] = None) -> bool:
    """
    Combine all annotated clips from a session into a single video

    Args:
        session_dir: Path to session directory
        output_dir: Optional output directory (defaults to session_dir itself)

    Returns:
        True if successful, False otherwise
    """
    print(f"🎯 Processing session: {session_dir}")

    # Get annotated clips
    clips = get_annotated_clips(session_dir)

    if not clips:
        print(f"❌ No annotated clips found in {session_dir}")
        return False

    print(f"📋 Found {len(clips)} annotated clips")

    # Determine output directory - save in session folder by default
    if output_dir is None:
        output_dir = session_dir

    # Create output filename
    output_filename = create_combined_filename(session_dir)
    output_path = os.path.join(output_dir, output_filename)

    print(f"💾 Output will be saved to: {output_path}")

    # Combine clips at 25 FPS
    success = combine_clips(clips, output_path, fps=25.0)

    if success:
        # Get file size
        file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        print(f"📁 File size: {file_size:.1f} MB")

    return success


def combine_all_sessions(base_output_dir: str = "data/output") -> None:
    """
    Combine clips from all sessions in the output directory
    
    Args:
        base_output_dir: Base output directory containing sessions
    """
    print("🔍 Searching for session directories...")
    
    session_dirs = find_session_directories(base_output_dir)
    
    if not session_dirs:
        print(f"❌ No session directories found in {base_output_dir}")
        return
    
    print(f"📂 Found {len(session_dirs)} session directories")
    
    successful = 0
    failed = 0
    
    for session_dir in session_dirs:
        print(f"\n{'='*60}")
        try:
            if combine_session_clips(session_dir):
                successful += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Error processing {session_dir}: {e}")
            failed += 1
    
    print(f"\n{'='*60}")
    print(f"📊 SUMMARY:")
    print(f"   ✅ Successful: {successful}")
    print(f"   ❌ Failed: {failed}")
    print(f"   📁 Total sessions: {len(session_dirs)}")


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description="Combine annotated clips from live sessions")
    parser.add_argument("--session", "-s", type=str, help="Specific session directory to process")
    parser.add_argument("--output", "-o", type=str, help="Output directory (default: session folder)")
    parser.add_argument("--base-dir", "-b", type=str, default="data/output", 
                       help="Base output directory (default: data/output)")
    parser.add_argument("--all", "-a", action="store_true", 
                       help="Process all sessions in base directory")
    parser.add_argument("--list", "-l", action="store_true", 
                       help="List available sessions")
    
    args = parser.parse_args()
    
    print("🎬 Session Clip Combiner")
    print("=" * 50)
    
    if args.list:
        # List available sessions
        sessions = find_session_directories(args.base_dir)
        if sessions:
            print(f"📂 Available sessions in {args.base_dir}:")
            for i, session in enumerate(sessions, 1):
                session_name = os.path.basename(session)
                clips = get_annotated_clips(session)
                print(f"   {i}. {session_name} ({len(clips)} clips)")
        else:
            print(f"❌ No sessions found in {args.base_dir}")
        return
    
    if args.all:
        # Process all sessions
        combine_all_sessions(args.base_dir)
    elif args.session:
        # Process specific session
        if not os.path.exists(args.session):
            print(f"❌ Session directory not found: {args.session}")
            return
        
        combine_session_clips(args.session, args.output)
    else:
        # Interactive mode
        sessions = find_session_directories(args.base_dir)
        if not sessions:
            print(f"❌ No sessions found in {args.base_dir}")
            return
        
        print(f"📂 Available sessions:")
        for i, session in enumerate(sessions, 1):
            session_name = os.path.basename(session)
            clips = get_annotated_clips(session)
            print(f"   {i}. {session_name} ({len(clips)} clips)")
        
        try:
            choice = input(f"\nSelect session (1-{len(sessions)}) or 'all': ").strip()
            
            if choice.lower() == 'all':
                combine_all_sessions(args.base_dir)
            else:
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(sessions):
                    combine_session_clips(sessions[choice_idx], args.output)
                else:
                    print("❌ Invalid selection")
        except (ValueError, KeyboardInterrupt):
            print("\n❌ Operation cancelled")


if __name__ == "__main__":
    main()
