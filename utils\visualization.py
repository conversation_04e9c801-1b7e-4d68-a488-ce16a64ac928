"""
Visualization utilities for bounding boxes and annotations
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional


def draw_bounding_boxes(frame: np.ndarray, boxes: List[Tuple], predictions: List[float], 
                       confidences: List[float], person_ids: List[int], 
                       threshold: float = 0.5) -> np.ndarray:
    """
    Draw bounding boxes with predictions on frame
    
    Args:
        frame: Input frame
        boxes: List of bounding boxes (x1, y1, x2, y2)
        predictions: List of prediction scores (0.0-1.0)
        confidences: List of detection confidences
        person_ids: List of person IDs
        threshold: Threshold for suspicious behavior
    
    Returns:
        Annotated frame
    """
    annotated_frame = frame.copy()
    
    for box, prediction, confidence, person_id in zip(boxes, predictions, confidences, person_ids):
        x1, y1, x2, y2 = map(int, box)
        
        # Choose color based on prediction
        if prediction > threshold:
            color = (0, 0, 255)  # Red for suspicious
            label = f"Person {person_id}: SHOPLIFTING ({prediction:.2f})"
        else:
            color = (0, 255, 0)  # Green for normal
            label = f"Person {person_id}: NORMAL ({prediction:.2f})"
        
        # Draw bounding box
        cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)
        
        # Draw label background
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10), 
                     (x1 + label_size[0], y1), color, -1)
        
        # Draw label text
        cv2.putText(annotated_frame, label, (x1, y1 - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # Draw confidence
        conf_text = f"Conf: {confidence:.2f}"
        cv2.putText(annotated_frame, conf_text, (x1, y2 + 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    return annotated_frame


def draw_simple_boxes(frame: np.ndarray, boxes: List[Tuple], labels: List[float], 
                     confidences: List[float], ids: List[int]) -> np.ndarray:
    """
    Draw simple bounding boxes (legacy format for main.py compatibility)
    
    Args:
        frame: Input frame
        boxes: List of bounding boxes
        labels: List of labels (0.0 = normal, 1.0 = suspicious)
        confidences: List of confidences
        ids: List of person IDs
    
    Returns:
        Annotated frame
    """
    annotated_frame = frame.copy()
    
    for box, label, conf, person_id in zip(boxes, labels, confidences, ids):
        x1, y1, x2, y2 = map(int, box)
        person_id = int(person_id)
        
        color = (0, 255, 0) if label == 0.0 else (0, 0, 255)
        text = "Normal" if label == 0.0 else "Shoplifting"
        
        cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)
        cv2.putText(annotated_frame, f"{text} ({conf:.2f})", (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
        cv2.putText(annotated_frame, f"{person_id}", (x1+10, y1 + 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    return annotated_frame


def draw_detection_boxes(frame: np.ndarray, boxes: List[Tuple], confidences: List[float], 
                        person_ids: List[int], color: Tuple[int, int, int] = (255, 255, 0)) -> np.ndarray:
    """
    Draw detection boxes without predictions (for processing state)
    
    Args:
        frame: Input frame
        boxes: List of bounding boxes
        confidences: List of detection confidences
        person_ids: List of person IDs
        color: Box color (BGR format)
    
    Returns:
        Annotated frame
    """
    annotated_frame = frame.copy()
    
    for box, confidence, person_id in zip(boxes, confidences, person_ids):
        x1, y1, x2, y2 = map(int, box)
        
        # Draw bounding box
        cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)
        
        # Draw label
        label = f"Person {person_id}: PROCESSING..."
        cv2.putText(annotated_frame, label, (x1, y1 - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # Draw confidence
        conf_text = f"Conf: {confidence:.2f}"
        cv2.putText(annotated_frame, conf_text, (x1, y2 + 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    return annotated_frame


def add_timestamp_overlay(frame: np.ndarray, timestamp: str, 
                         position: Tuple[int, int] = (10, 30)) -> np.ndarray:
    """
    Add timestamp overlay to frame
    
    Args:
        frame: Input frame
        timestamp: Timestamp string
        position: Text position (x, y)
    
    Returns:
        Frame with timestamp overlay
    """
    annotated_frame = frame.copy()
    
    timestamp_text = f"Time: {timestamp}"
    cv2.putText(annotated_frame, timestamp_text, position, 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    return annotated_frame


def add_session_info(frame: np.ndarray, session_id: str, 
                    position: Tuple[int, int] = (10, 60)) -> np.ndarray:
    """
    Add session information overlay to frame
    
    Args:
        frame: Input frame
        session_id: Session identifier
        position: Text position (x, y)
    
    Returns:
        Frame with session info overlay
    """
    annotated_frame = frame.copy()
    
    session_text = f"Session: {session_id}"
    cv2.putText(annotated_frame, session_text, position, 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
    
    return annotated_frame


def save_annotated_video(frames: List[np.ndarray], output_path: str,
                        fps: float = 16.0) -> bool:
    """
    Save list of annotated frames as video
    
    Args:
        frames: List of annotated frames
        output_path: Output video path
        fps: Frames per second
    
    Returns:
        True if successful, False otherwise
    """
    if not frames:
        return False
    
    try:
        # Get frame dimensions
        h, w = frames[0].shape[:2]
        
        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (w, h))
        
        # Write frames
        for frame in frames:
            out.write(frame)
        
        out.release()
        return True
        
    except Exception as e:
        print(f"Error saving annotated video to {output_path}: {e}")
        return False
