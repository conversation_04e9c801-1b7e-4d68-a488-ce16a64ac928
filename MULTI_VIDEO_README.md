# Multi-Video Processor

A powerful script that processes multiple videos in parallel and displays their annotated output in a real-time grid layout. Perfect for monitoring multiple video feeds simultaneously with shoplifting detection.

## 🌟 Features

- **Parallel Processing**: Process multiple videos simultaneously using multithreading
- **Grid Display**: Real-time grid layout showing all videos within 720p resolution
- **Person Detection**: YOLO-based person detection with tracking
- **Shoplifting Predictions**: ActionTransformer model predictions with visual feedback
- **Optimized Performance**: Frame skipping and reduced processing load for smooth operation
- **Automatic Layout**: Calculates optimal grid layout (2x2, 3x3, etc.) based on video count
- **Interactive Controls**: Pause/resume and quit functionality

## 🚀 Quick Start

### 1. Basic Usage

```python
from multi_video_processor import MultiVideoProcessor

# Set your video folder
processor = MultiVideoProcessor("data/feed", max_videos=4)

# Start processing and display
processor.process_videos()
```

### 2. Using the Script Directly

1. **Set your input folder** at the top of `multi_video_processor.py`:
   ```python
   INPUT_FOLDER = "path/to/your/videos"  # Change this path
   ```

2. **Run the script**:
   ```bash
   python multi_video_processor.py
   ```

3. **Controls**:
   - Press `q` to quit
   - Press `p` to pause/resume

### 3. Using the Example Script

```bash
python example_multi_video.py
```

This provides a menu with different configuration options.

## 📁 Folder Structure

```
your-video-folder/
├── video1.mp4
├── video2.avi
├── video3.mov
└── video4.mkv
```

**Note**: Only videos in the main folder are processed (subfolders are excluded).

## 🎛️ Configuration Options

### Grid Layouts

| Videos | Grid Layout | Tile Size (approx.) |
|--------|-------------|---------------------|
| 1      | 1x1         | 1280x720           |
| 2      | 2x1         | 640x720            |
| 3-4    | 2x2         | 640x360            |
| 5-6    | 3x2         | 426x360            |
| 7-9    | 3x3         | 426x240            |

### Performance Settings

The script is optimized for performance with:
- **Frame Skipping**: Processes every 5th frame
- **Higher Confidence Threshold**: 0.3 for YOLO detection
- **Reduced Processing Load**: Optimized for real-time display
- **Limited Video Count**: Default maximum of 4 videos

## 🔧 Customization

### Change Maximum Videos

```python
processor = MultiVideoProcessor("data/feed", max_videos=6)  # Process 6 videos
```

### Modify Performance Settings

Edit these values in the script:
```python
skip_frames=4,  # Process every 5th frame (0-based)
confidence_threshold=0.3  # YOLO confidence threshold
```

### Window Size Limits

```python
MAX_WINDOW_WIDTH = 1280  # 720p width
MAX_WINDOW_HEIGHT = 720  # 720p height
```

## 📊 Output

### Visual Display
- **Green Boxes**: Normal behavior detected
- **Red Boxes**: Suspicious behavior detected
- **Blue Boxes**: Person detected, processing...
- **Video Names**: Displayed on each tile
- **Processing Info**: Frame count, clips, predictions

### File Output
Each video creates its own session folder:
```
data/output/multi_video_[timestamp]/
├── video1_[timestamp]/
│   ├── feed/          # Original clips
│   └── annotated/     # Annotated clips
├── video2_[timestamp]/
└── ...
```

## 🎯 Supported Video Formats

- `.mp4` (recommended)
- `.avi`
- `.mov`
- `.mkv`
- `.wmv`
- `.flv`
- `.webm`

## ⚡ Performance Tips

1. **Use SSD Storage**: Faster I/O for multiple video streams
2. **CUDA GPU**: Required for optimal performance
3. **Limit Video Count**: Start with 2-4 videos, increase as needed
4. **Video Resolution**: Lower resolution videos process faster
5. **Close Other Applications**: Free up system resources

## 🐛 Troubleshooting

### Common Issues

**"No video files found"**
- Check the INPUT_FOLDER path
- Ensure videos are in the main folder (not subfolders)
- Verify video file extensions are supported

**Slow Performance**
- Reduce max_videos count
- Increase skip_frames value
- Check GPU memory usage
- Close other applications

**OpenCV Window Not Showing**
- Ensure you're running in a GUI environment
- Check if OpenCV is properly installed
- Try running with fewer videos first

### Error Messages

**"Could not open video file"**
- Video file may be corrupted
- Unsupported codec
- File permissions issue

**"CUDA out of memory"**
- Reduce number of videos
- Lower video resolution
- Restart the script

## 📋 Requirements

- Python 3.8+
- OpenCV (cv2)
- PyTorch with CUDA support
- YOLO model files
- ActionTransformer model files
- Sufficient GPU memory for parallel processing

## 🔄 Integration

This script integrates with the existing video processing pipeline:
- Uses `VideoFileProcessor` for individual video processing
- Leverages existing YOLO and ActionTransformer models
- Maintains compatibility with the current project structure

## 📝 Example Configurations

### Security Monitoring (4 cameras)
```python
processor = MultiVideoProcessor("security_feeds", max_videos=4)
```

### Batch Analysis (2 videos)
```python
processor = MultiVideoProcessor("analysis_videos", max_videos=2)
```

### Large Scale Processing (9 videos)
```python
processor = MultiVideoProcessor("all_videos", max_videos=9)
```

---

**Note**: This script is designed to work within the existing shoplifting detection project structure. Make sure all required models and dependencies are properly installed before use.
