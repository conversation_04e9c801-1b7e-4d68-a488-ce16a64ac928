import cv2
import os
import torch
import threading
import queue
import time
import numpy as np
from collections import defaultdict, deque
import datetime as dt
from typing import List, Tuple, Dict, Optional

# Import utilities
from utils.detection import detect_persons, reset_yolo_tracker, get_tracker_info, initialize_yolo_tracker
from utils.cropping import crop_person_simple, save_person_clip
from utils.prediction import predict_video_clip, predict_with_timing
from utils.visualization import draw_bounding_boxes, add_timestamp_overlay, save_annotated_video
from utils.video_processing import create_session_directory, create_timestamped_filename
from utils.models import initialize_models, get_device, validate_models, load_yolo_model, load_action_transformer

from config import LiveConfig


class LiveVideoProcessor:
    """
    Multi-threaded live video processor for real-time shoplifting detection.

    Thread 1: Webcam capture → 16-frame clipping
    Thread 2: YOLO person tracking → cropping → saving crops
    Thread 3: Predictions → bounding box drawing → saving annotated clips
    """

    def __init__(self,
                 webcam_id: int = 0,
                 model_path: str = 'data/models/trained_model_224.pt',
                 yolo_path: str = 'data/models/yolo11s.pt',
                 output_dir: str = 'data/output',
                 frame_buffer_size: int = 16,
                 crop_size: Tuple[int, int] = (224, 224),
                 confidence_threshold: float = 0.1):

        # Configuration
        self.webcam_id = webcam_id
        self.model_path = model_path
        self.yolo_path = yolo_path
        self.output_dir = output_dir
        self.frame_buffer_size = frame_buffer_size
        self.crop_size = crop_size
        self.confidence_threshold = confidence_threshold

        # Create timestamped session directory
        self.session_timestamp = dt.datetime.now().strftime("%d-%m-%Y_%H-%M-%S")
        self.session_dir = os.path.join(output_dir, self.session_timestamp)

        # Create output directories with timestamp
        self.feed_dir = os.path.join(self.session_dir, 'feed')  # For webcam clips
        self.annotated_dir = os.path.join(self.session_dir, 'annotated')  # For annotated clips

        os.makedirs(self.feed_dir, exist_ok=True)
        os.makedirs(self.annotated_dir, exist_ok=True)

        print(f"📅 Session started at: {self.session_timestamp}")
        print(f"📁 Session directory: {self.session_dir}")
        print(f"📁 Feed clips will be saved to: {self.feed_dir}")
        print(f"📁 Annotated clips will be saved to: {self.annotated_dir}")

        # Device setup
        self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")

        # Initialize models
        self._initialize_models()

        # Threading components
        self.frame_buffer = deque(maxlen=frame_buffer_size)
        self.clip_queue = queue.Queue(maxsize=10)  # Queue for 16-frame clips
        self.detection_queue = queue.Queue(maxsize=10)  # Queue for person detection/cropping
        self.prediction_queue = queue.Queue(maxsize=10)  # Queue for predictions
        self.result_queue = queue.Queue()  # Queue for final results

        # Control flags
        self.running = False
        self.quit_requested = False  # Track if 'q' was pressed
        self.capture_thread = None
        self.detection_thread = None
        self.prediction_thread = None

        # Shared data for bounding box drawing
        self.current_detections = {}  # person_id -> (box, prediction, confidence)
        self.detection_lock = threading.Lock()

        # Statistics
        self.frame_count = 0
        self.clip_count = 0
        self.prediction_count = 0

        # Duplicate prevention tracking
        self.created_annotated_clips = set()  # Track created annotated clip filenames

    def _initialize_models(self):
        """Initialize YOLO and ActionTransformer models"""
        print("Loading models...")

        # Load models using utils
        self.action_model = load_action_transformer(self.model_path, self.device)
        self.yolo_model = load_yolo_model(self.yolo_path, self.device)

        # Initialize YOLO tracker
        initialize_yolo_tracker(self.yolo_model)

        # Validate models
        if not validate_models(self.action_model, self.yolo_model):
            raise RuntimeError("Model validation failed!")

        print("Models loaded successfully!")

    def _detect_persons(self, frame: np.ndarray) -> Tuple[List, List, List]:
        """
        Detect persons in frame using YOLO
        Returns: (boxes, confidences, track_ids)
        """
        device_index = self.device.index if self.device.type == 'cuda' else None
        return detect_persons(self.yolo_model, frame, self.confidence_threshold, device_index)

    def _reset_yolo_tracker(self):
        """
        Reset YOLO tracker to start fresh tracking for new clip
        """
        reset_yolo_tracker(self.yolo_model)

    def _get_tracker_info(self):
        """
        Get information about the current YOLO tracker state
        """
        return get_tracker_info(self.yolo_model)

    def _crop_person(self, frame: np.ndarray, box: Tuple[int, int, int, int]) -> np.ndarray:
        """
        Crop person from frame and resize to target size
        """
        return crop_person_simple(frame, box, self.crop_size)

    def _save_person_clip(self, crops: List[np.ndarray], person_id: int, timestamp: str) -> str:
        """
        Save cropped frames as a video clip in the feed folder
        Returns: path to saved clip
        """
        clip_filename = f"person_{person_id}_{timestamp}.mp4"
        clip_path = os.path.join(self.feed_dir, clip_filename)

        save_person_clip(crops, clip_path, fps=16.0)  # 16 FPS for individual clips
        return clip_path

    def _process_frame_buffer(self) -> Dict[int, List[np.ndarray]]:
        """
        Process current frame buffer to extract person crops
        Returns: Dictionary mapping person_id to list of crops
        """
        if len(self.frame_buffer) < self.frame_buffer_size:
            return {}

        person_crops = defaultdict(list)

        # Process each frame in buffer
        for frame in self.frame_buffer:
            boxes, confidences, ids = self._detect_persons(frame)

            # Extract crops for each detected person
            for box, conf, person_id in zip(boxes, confidences, ids):
                if conf > self.confidence_threshold:
                    crop = self._crop_person(frame, box)
                    person_crops[person_id].append(crop)

        # Filter out persons with insufficient frames
        filtered_crops = {}
        min_frames = max(1, self.frame_buffer_size // 2)  # At least half the frames

        for person_id, crops in person_crops.items():
            if len(crops) >= min_frames:
                # Ensure we have exactly frame_buffer_size frames by padding if needed
                while len(crops) < self.frame_buffer_size:
                    crops.append(crops[-1])  # Duplicate last frame
                filtered_crops[person_id] = crops[:self.frame_buffer_size]

        return filtered_crops

    def _capture_thread_worker(self):
        """
        Thread 1: Webcam capture and 16-frame clipping
        """
        print("Starting capture thread...")

        # Initialize webcam
        cap = cv2.VideoCapture(self.webcam_id)
        if not cap.isOpened():
            print(f"Error: Could not open webcam {self.webcam_id}")
            return

        # Set webcam properties for better performance
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)

        print("Webcam initialized successfully")

        try:
            while self.running:
                ret, frame = cap.read()
                if not ret:
                    print("Warning: Failed to read frame from webcam")
                    continue

                # Add frame to buffer
                self.frame_buffer.append(frame.copy())
                self.frame_count += 1

                # When buffer is full, send 16-frame clip to detection thread
                if len(self.frame_buffer) == self.frame_buffer_size:
                    timestamp = dt.datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]

                    # Create clip data
                    clip_data = {
                        'frames': list(self.frame_buffer),  # Copy all 16 frames
                        'timestamp': timestamp,
                        'frame_count': self.frame_count
                    }

                    # Send to detection thread (non-blocking)
                    try:
                        self.clip_queue.put_nowait(clip_data)
                        self.clip_count += 1
                        print(f"📹 Created clip {self.clip_count}: {timestamp} (frames: {self.frame_count-15}-{self.frame_count})")
                    except queue.Full:
                        print("Warning: Clip queue full, skipping clip")

                    # CRITICAL FIX: Clear the buffer after creating a clip to prevent duplicates
                    self.frame_buffer.clear()
                    print(f"🧹 Buffer cleared after clip {self.clip_count}")

                # Display live feed with current detections
                display_frame = self._draw_current_detections(frame.copy())
                cv2.imshow('Live Feed', display_frame)
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    print("\n🛑 'q' pressed - stopping system...")
                    self.quit_requested = True
                    self.running = False
                    break

        except Exception as e:
            print(f"Error in capture thread: {e}")
        finally:
            cap.release()
            cv2.destroyAllWindows()
            print("Capture thread stopped")

            # If 'q' was pressed, mark it for the main stop sequence
            if self.quit_requested:
                print("🎬 'q' pressed - will trigger clip combination after all threads stop...")

    def _draw_current_detections(self, frame):
        """
        Draw current detection bounding boxes on frame
        """
        with self.detection_lock:
            for person_id, detection_data in self.current_detections.items():
                box, prediction, confidence = detection_data
                x1, y1, x2, y2 = box

                # Choose color based on prediction
                if prediction is not None:
                    if prediction > 0.5:  # Suspicious
                        color = (0, 0, 255)  # Red
                        label = f"Person {person_id}: SHOPLIFTING ({prediction:.2f})"
                    else:  # Normal
                        color = (0, 255, 0)  # Green
                        label = f"Person {person_id}: NORMAL ({prediction:.2f})"
                else:
                    color = (255, 255, 0)  # Yellow (processing)
                    label = f"Person {person_id}: PROCESSING..."

                # Draw bounding box
                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)

                # Draw label background
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(frame, (x1, y1 - label_size[1] - 10),
                             (x1 + label_size[0], y1), color, -1)

                # Draw label text
                cv2.putText(frame, label, (x1, y1 - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                # Draw confidence
                conf_text = f"Conf: {confidence:.2f}"
                cv2.putText(frame, conf_text, (x1, y2 + 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        return frame

    def _detection_thread_worker(self):
        """
        Thread 2: YOLO person tracking, cropping, and saving crops
        """
        print("Starting detection thread...")

        try:
            while self.running or not self.clip_queue.empty():
                try:
                    # Get 16-frame clip from capture thread
                    clip_data = self.clip_queue.get(timeout=1.0)

                    frames = clip_data['frames']
                    timestamp = clip_data['timestamp']
                    frame_count = clip_data['frame_count']

                    # Reset YOLO tracker for fresh tracking on this clip
                    self._reset_yolo_tracker()

                    # Process each frame for person detection
                    print(f"🔍 Processing clip {timestamp} with {len(frames)} frames (clip #{self.clip_count})")
                    person_crops = defaultdict(list)
                    person_boxes = defaultdict(list)  # Store boxes for each frame for each person
                    person_confidences = defaultdict(list)  # Store confidences for each frame
                    frame_detections = []  # Store detection data for each frame

                    for frame_idx, frame in enumerate(frames):
                        boxes, confidences, ids = self._detect_persons(frame)

                        # Store frame-level detection data
                        frame_detection = {
                            'frame_idx': frame_idx,
                            'boxes': boxes,
                            'confidences': confidences,
                            'ids': ids
                        }
                        frame_detections.append(frame_detection)

                        # Extract crops for each detected person
                        for box, conf, person_id in zip(boxes, confidences, ids):
                            if conf > self.confidence_threshold:
                                crop = self._crop_person(frame, box)
                                person_crops[person_id].append(crop)
                                person_boxes[person_id].append(box)  # Store box for this frame
                                person_confidences[person_id].append(conf)

                    # Filter persons with sufficient frames and save crops
                    min_frames = max(1, self.frame_buffer_size // 2)

                    for person_id, crops in person_crops.items():
                        if len(crops) >= min_frames:
                            # Pad crops to exactly frame_buffer_size frames
                            while len(crops) < self.frame_buffer_size:
                                crops.append(crops[-1])  # Duplicate last frame
                            crops = crops[:self.frame_buffer_size]

                            # Save person clip
                            clip_path = self._save_person_clip(crops, person_id, timestamp)

                            # Get latest box and average confidence for display
                            latest_box = person_boxes[person_id][-1] if person_boxes[person_id] else None
                            avg_confidence = sum(person_confidences[person_id]) / len(person_confidences[person_id])

                            # Update current detections for display
                            with self.detection_lock:
                                if latest_box is not None:
                                    self.current_detections[person_id] = (
                                        latest_box,
                                        None,  # Prediction not available yet
                                        avg_confidence
                                    )

                            # Send to prediction thread with frame-level detection data
                            prediction_data = {
                                'clip_path': clip_path,
                                'person_id': person_id,
                                'timestamp': timestamp,
                                'frame_count': frame_count,
                                'latest_box': latest_box,
                                'avg_confidence': avg_confidence,
                                'original_frames': frames,  # For final video saving
                                'frame_detections': frame_detections  # Frame-level detection data
                            }

                            try:
                                self.detection_queue.put_nowait(prediction_data)
                            except queue.Full:
                                print("Warning: Detection queue full, skipping detection")

                    # Mark task as done
                    self.clip_queue.task_done()

                except queue.Empty:
                    continue  # Timeout, check running flag and continue

        except Exception as e:
            print(f"Error in detection thread: {e}")
        finally:
            print("Detection thread stopped")

    def _predict_clip(self, clip_path: str) -> float:
        """
        Make prediction on a video clip using ActionTransformer
        Returns: prediction score (0.0 = normal, 1.0 = suspicious)
        """
        return predict_video_clip(self.action_model, clip_path, self.device, warmup_runs=2)

    def _save_annotated_clip(self, frames, person_data, timestamp, frame_detections=None):
        """
        Save annotated video clip with frame-accurate bounding boxes and predictions
        """
        annotated_filename = f"annotated_{timestamp}.mp4"
        annotated_path = os.path.join(self.annotated_dir, annotated_filename)

        # DUPLICATE PREVENTION: Check if file already exists or was already created
        if annotated_filename in self.created_annotated_clips:
            print(f"⚠️  Annotated clip already tracked as created: {annotated_filename} - SKIPPING")
            return annotated_path

        if os.path.exists(annotated_path):
            print(f"⚠️  Annotated clip file already exists: {annotated_filename} - SKIPPING to prevent duplicate")
            self.created_annotated_clips.add(annotated_filename)  # Track it
            return annotated_path

        # Get frame dimensions
        h, w = frames[0].shape[:2]

        # Create video writer at 16 FPS for individual clips
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(annotated_path, fourcc, 16.0, (w, h))

        # Annotate each frame with frame-accurate bounding boxes
        for frame_idx, frame in enumerate(frames):
            annotated_frame = frame.copy()

            # If we have frame-level detection data, use it for accurate bounding boxes
            if frame_detections and frame_idx < len(frame_detections):
                frame_detection = frame_detections[frame_idx]
                boxes = frame_detection['boxes']
                confidences = frame_detection['confidences']
                ids = frame_detection['ids']

                # Draw bounding boxes for detected persons in this specific frame
                for box, conf, person_id in zip(boxes, confidences, ids):
                    if person_id in person_data:  # Only draw if we have prediction for this person
                        prediction = person_data[person_id]['prediction']

                        x1, y1, x2, y2 = map(int, box)

                        # Choose color and label based on prediction
                        if prediction > 0.5:  # Suspicious
                            color = (0, 0, 255)  # Red
                            label = f"Person {person_id}: SHOPLIFTING ({prediction:.2f})"
                        else:  # Normal
                            color = (0, 255, 0)  # Green
                            label = f"Person {person_id}: NORMAL ({prediction:.2f})"

                        # Draw bounding box
                        cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)

                        # Draw label background
                        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                        cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10),
                                     (x1 + label_size[0], y1), color, -1)

                        # Draw label text
                        cv2.putText(annotated_frame, label, (x1, y1 - 5),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                        # Draw confidence
                        conf_text = f"Conf: {conf:.2f}"
                        cv2.putText(annotated_frame, conf_text, (x1, y2 + 20),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            else:
                # Fallback: use stored bounding boxes (less accurate)
                for person_id, data in person_data.items():
                    box = data['box']
                    prediction = data['prediction']
                    confidence = data['confidence']

                    x1, y1, x2, y2 = map(int, box)

                    # Choose color and label based on prediction
                    if prediction > 0.5:  # Suspicious
                        color = (0, 0, 255)  # Red
                        label = f"Person {person_id}: SHOPLIFTING ({prediction:.2f})"
                    else:  # Normal
                        color = (0, 255, 0)  # Green
                        label = f"Person {person_id}: NORMAL ({prediction:.2f})"

                    # Draw bounding box
                    cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)

                    # Draw label
                    cv2.putText(annotated_frame, label, (x1, y1 - 5),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # Add timestamp to frame
            timestamp_text = f"Time: {timestamp}"
            cv2.putText(annotated_frame, timestamp_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            out.write(annotated_frame)

        out.release()

        # Track this clip as created to prevent duplicates
        self.created_annotated_clips.add(annotated_filename)
        print(f"✅ Created annotated clip: {annotated_filename} (with {len(person_data)} persons)")

        return annotated_path

    def _cleanup_pending_clips(self, pending_clips):
        """
        Create annotated clips for timestamps that are ready (no new persons expected)
        """
        current_time = time.time()
        timestamps_to_process = []

        for timestamp, clip_info in pending_clips.items():
            if not clip_info['annotated'] and clip_info['persons']:
                # If we haven't seen new persons for this timestamp in 1 second, process it
                time_since_update = current_time - clip_info['last_update']
                if time_since_update > 1.0:  # 1 second timeout
                    timestamps_to_process.append(timestamp)

        for timestamp in timestamps_to_process:
            try:
                clip_info = pending_clips[timestamp]

                # Double-check that it hasn't been annotated yet (race condition prevention)
                if not clip_info['annotated']:
                    annotated_path = self._save_annotated_clip(
                        clip_info['frames'],
                        clip_info['persons'],
                        timestamp,
                        clip_info['frame_detections']
                    )
                    clip_info['annotated'] = True
                    print(f"📹 Cleanup save: {annotated_path} (after {len(clip_info['persons'])} persons)")
                else:
                    print(f"📹 Timestamp {timestamp} already annotated - skipping cleanup save")
            except Exception as e:
                print(f"❌ Error in cleanup save for {timestamp}: {e}")

    def _prediction_thread_worker(self):
        """
        Thread 3: Predictions, bounding box drawing, and saving annotated clips
        """
        print("Starting prediction thread...")

        # Group predictions by timestamp to create complete annotated clips
        pending_clips = {}  # timestamp -> {person_id: data}
        last_cleanup_time = time.time()

        try:
            while self.running or not self.detection_queue.empty():
                # Periodic cleanup: create annotated clips for old timestamps
                current_time = time.time()
                if current_time - last_cleanup_time > 2.0:  # Cleanup every 2 seconds
                    self._cleanup_pending_clips(pending_clips)
                    last_cleanup_time = current_time
                try:
                    # Get detection data from detection thread
                    detection_data = self.detection_queue.get(timeout=1.0)

                    # Make prediction
                    start_time = time.time()
                    prediction = self._predict_clip(detection_data['clip_path'])
                    prediction_time = time.time() - start_time

                    person_id = detection_data['person_id']
                    timestamp = detection_data['timestamp']

                    # Update current detections for live display
                    with self.detection_lock:
                        if person_id in self.current_detections:
                            box, _, confidence = self.current_detections[person_id]
                            self.current_detections[person_id] = (box, prediction, confidence)

                    # Store prediction data for this clip
                    if timestamp not in pending_clips:
                        pending_clips[timestamp] = {
                            'persons': {},
                            'frames': detection_data['original_frames'],
                            'frame_count': detection_data['frame_count'],
                            'frame_detections': detection_data.get('frame_detections', None),
                            'annotated': False,  # Track if annotated clip was already created
                            'last_update': time.time(),  # Track when last person was added
                            'expected_persons': set(),  # Track expected persons for this timestamp
                            'completed_persons': set()  # Track completed predictions
                        }

                    # Add this person to the clip data
                    pending_clips[timestamp]['persons'][person_id] = {
                        'prediction': prediction,
                        'box': detection_data['latest_box'],
                        'confidence': detection_data['avg_confidence'],
                        'clip_path': detection_data['clip_path']
                    }

                    # Mark this person as completed and update timestamp
                    pending_clips[timestamp]['completed_persons'].add(person_id)
                    pending_clips[timestamp]['last_update'] = time.time()

                    # Create result
                    result = {
                        'person_id': person_id,
                        'timestamp': timestamp,
                        'frame_count': detection_data['frame_count'],
                        'clip_path': detection_data['clip_path'],
                        'prediction': prediction,
                        'prediction_time': prediction_time,
                        'is_suspicious': prediction > 0.5,
                        'box': detection_data['latest_box']
                    }

                    # Add to result queue
                    self.result_queue.put(result)
                    self.prediction_count += 1

                    # Print result
                    status = "🚨 SUSPICIOUS" if result['is_suspicious'] else "✅ NORMAL"
                    print(f"{status} - Person {person_id} at {timestamp}: "
                          f"score {prediction:.3f} (time: {prediction_time:.3f}s)")

                    # Don't create annotated clip immediately - wait for potential other persons
                    # The clip will be created by the cleanup process or when system stops
                    print(f"📝 Person {person_id} prediction completed for timestamp {timestamp}")
                    print(f"   Persons so far: {list(pending_clips[timestamp]['persons'].keys())}")

                    # Clean up individual person clip (optional)
                    # os.remove(detection_data['clip_path'])

                    # Mark task as done
                    self.detection_queue.task_done()

                except queue.Empty:
                    continue  # Timeout, check running flag and continue

        except Exception as e:
            print(f"Error in prediction thread: {e}")
        finally:
            # Create annotated clips for any remaining pending clips
            remaining_clips = [ts for ts, info in pending_clips.items()
                             if not info['annotated'] and info['persons']]

            if remaining_clips:
                print(f"Creating annotated clips for {len(remaining_clips)} remaining timestamps...")
                for timestamp in remaining_clips:
                    clip_info = pending_clips[timestamp]
                    try:
                        # Final check to prevent duplicates
                        if not clip_info['annotated']:
                            annotated_path = self._save_annotated_clip(
                                clip_info['frames'],
                                clip_info['persons'],
                                timestamp,
                                clip_info['frame_detections']
                            )
                            clip_info['annotated'] = True
                            print(f"📹 Final save: {annotated_path} (with {len(clip_info['persons'])} persons)")
                        else:
                            print(f"📹 Timestamp {timestamp} already processed - skipping final save")
                    except Exception as e:
                        print(f"❌ Error saving final annotated clip for {timestamp}: {e}")
            else:
                print("No remaining clips to process")

            print("Prediction thread stopped")

    def start(self):
        """Start the live video processing system"""
        if self.running:
            print("System is already running!")
            return

        print("Starting 3-thread live video processing system...")
        self.running = True

        # Start all three threads
        self.capture_thread = threading.Thread(target=self._capture_thread_worker, daemon=True)
        self.detection_thread = threading.Thread(target=self._detection_thread_worker, daemon=True)
        self.prediction_thread = threading.Thread(target=self._prediction_thread_worker, daemon=True)

        self.capture_thread.start()
        self.detection_thread.start()
        self.prediction_thread.start()

        print("✅ All threads started successfully!")
        print("📹 Thread 1: Webcam capture and 16-frame clipping")
        print("🔍 Thread 2: YOLO detection, cropping, and saving")
        print("🧠 Thread 3: Predictions and annotated clip creation")
        print("Press 'q' in the video window to stop, or call stop() method")

    def stop(self):
        """Stop the live video processing system"""
        if not self.running:
            if self.quit_requested:
                print("System was already stopped by 'q' key - proceeding with cleanup...")
            else:
                print("System is not running!")
                return

        if self.quit_requested:
            print("Stopping live video processing system (initiated by 'q' key)...")
        else:
            print("Stopping live video processing system...")
        self.running = False

        # Wait for all threads to finish
        threads = [
            ("Capture", self.capture_thread),
            ("Detection", self.detection_thread),
            ("Prediction", self.prediction_thread)
        ]

        for name, thread in threads:
            if thread and thread.is_alive():
                print(f"Waiting for {name} thread to stop...")
                thread.join(timeout=5.0)
                if thread.is_alive():
                    print(f"Warning: {name} thread did not stop gracefully")

        print("System stopped successfully!")

        # Automatically combine session clips
        if self.quit_requested:
            print(f"\n🎬 'q' was pressed - processing session clips for combination...")
        else:
            print(f"\n🎬 Processing session clips for combination...")

        combined_path = self.combine_session_clips(auto_combine=True)

        if combined_path:
            print(f"🎉 Session complete! Combined video saved to:")
            print(f"   📁 {combined_path}")
        else:
            print(f"ℹ️  No annotated clips to combine for this session")

    def get_statistics(self) -> Dict:
        """Get current processing statistics"""
        return {
            'session_timestamp': self.session_timestamp,
            'session_directory': self.session_dir,
            'frames_processed': self.frame_count,
            'clips_created': self.clip_count,
            'predictions_made': self.prediction_count,
            'pending_clips': self.clip_queue.qsize(),
            'pending_detections': self.detection_queue.qsize(),
            'results_available': self.result_queue.qsize(),
            'active_persons': len(self.current_detections),
            'tracker_info': self._get_tracker_info(),
            'is_running': self.running
        }

    def get_latest_results(self, max_results: int = 10) -> List[Dict]:
        """Get latest prediction results"""
        results = []
        count = 0

        while not self.result_queue.empty() and count < max_results:
            try:
                results.append(self.result_queue.get_nowait())
                count += 1
            except queue.Empty:
                break

        return results

    def combine_session_clips(self, auto_combine: bool = True) -> Optional[str]:
        """
        Combine all annotated clips from this session into a single video
        Saves the combined video in the session folder

        Args:
            auto_combine: Whether to automatically combine clips

        Returns:
            Path to combined video if successful, None otherwise
        """
        if not auto_combine:
            return None

        try:
            # Check if annotated clips exist
            annotated_dir = os.path.join(self.session_dir, "annotated")
            if not os.path.exists(annotated_dir):
                print(f"ℹ️  No annotated folder found in session directory")
                return None

            # Count annotated clips
            import glob
            clip_pattern = os.path.join(annotated_dir, "annotated_*.mp4")
            clips = glob.glob(clip_pattern)

            if not clips:
                print(f"ℹ️  No annotated clips found to combine")
                return None

            print(f"📋 Found {len(clips)} annotated clips to combine")

            from combine_session_clips import combine_session_clips

            print(f"🎬 Combining session clips...")
            # Save combined video in the session folder itself
            success = combine_session_clips(self.session_dir, self.session_dir)

            if success:
                combined_filename = f"combined_session_{self.session_timestamp}.mp4"
                combined_path = os.path.join(self.session_dir, combined_filename)

                # Verify the file was created and get its size
                if os.path.exists(combined_path):
                    file_size = os.path.getsize(combined_path) / (1024 * 1024)  # MB
                    print(f"✅ Session clips combined successfully!")
                    print(f"   📁 File: {combined_path}")
                    print(f"   📊 Size: {file_size:.1f} MB")
                    print(f"   🎬 Clips: {len(clips)} combined")
                    return combined_path
                else:
                    print("❌ Combined video file was not created")
                    return None
            else:
                print("❌ Failed to combine session clips")
                return None

        except ImportError:
            print(f"❌ Error: combine_session_clips module not found")
            print(f"💡 Make sure combine_session_clips.py is in the project directory")
            return None
        except Exception as e:
            print(f"❌ Error combining session clips: {e}")
            import traceback
            traceback.print_exc()
            return None

    def force_combine_clips(self) -> Optional[str]:
        """
        Force combination of session clips (can be called manually)

        Returns:
            Path to combined video if successful, None otherwise
        """
        print(f"🔧 Manually combining session clips...")
        return self.combine_session_clips(auto_combine=True)

    def get_session_info(self) -> Dict:
        """
        Get information about the current session

        Returns:
            Dictionary with session information
        """
        annotated_dir = os.path.join(self.session_dir, "annotated")
        feed_dir = os.path.join(self.session_dir, "feed")

        # Count clips
        annotated_clips = 0
        feed_clips = 0

        if os.path.exists(annotated_dir):
            import glob
            annotated_clips = len(glob.glob(os.path.join(annotated_dir, "annotated_*.mp4")))

        if os.path.exists(feed_dir):
            import glob
            feed_clips = len(glob.glob(os.path.join(feed_dir, "clip_*.mp4")))

        # Check for combined video
        combined_filename = f"combined_session_{self.session_timestamp}.mp4"
        combined_path = os.path.join(self.session_dir, combined_filename)
        combined_exists = os.path.exists(combined_path)
        combined_size = 0

        if combined_exists:
            combined_size = os.path.getsize(combined_path) / (1024 * 1024)  # MB

        return {
            'session_timestamp': self.session_timestamp,
            'session_directory': self.session_dir,
            'annotated_clips': annotated_clips,
            'feed_clips': feed_clips,
            'combined_video_exists': combined_exists,
            'combined_video_path': combined_path if combined_exists else None,
            'combined_video_size_mb': combined_size,
            'is_running': self.running
        }


def create_processor_from_config(config_class=LiveConfig):
    """
    Create a LiveVideoProcessor using a configuration class
    """
    config_class.create_output_dir()
    config_class.validate_models()

    return LiveVideoProcessor(**config_class.get_processor_config())


def main():
    """
    Main function to run the 3-thread live video processing system
    """
    print("=" * 70)
    print("🎥 Live Shoplifting Detection System - 3-Thread Architecture")
    print("=" * 70)
    print("📹 Thread 1: Webcam capture → 16-frame clipping")
    print("🔍 Thread 2: YOLO detection → cropping → saving crops")
    print("🧠 Thread 3: Predictions → bounding boxes → annotated clips")
    print("=" * 70)

    # Initialize processor
    processor = LiveVideoProcessor(
        webcam_id=0,  # Default webcam
        model_path='data/models/trained_model_224.pt',
        yolo_path='data/models/yolo11s.pt',
        output_dir='data/output/live_clips',
        frame_buffer_size=16,
        crop_size=(224, 224),
        confidence_threshold=0.1
    )

    try:
        # Start processing
        processor.start()

        # Monitor system
        print("\nSystem is running. Monitoring statistics...")
        print("Press Ctrl+C to stop the system")

        while processor.running:
            time.sleep(5)  # Update every 5 seconds

            # Print statistics
            stats = processor.get_statistics()
            print(f"\n📊 Stats: Frames: {stats['frames_processed']}, "
                  f"Clips: {stats['clips_created']}, "
                  f"Predictions: {stats['predictions_made']}")
            print(f"   Queues - Clips: {stats['pending_clips']}, "
                  f"Detections: {stats['pending_detections']}, "
                  f"Active Persons: {stats['active_persons']}")

            # Print recent results
            results = processor.get_latest_results(5)
            for result in results:
                status = "🚨 SUSPICIOUS" if result['is_suspicious'] else "✅ NORMAL"
                print(f"  {status} - Person {result['person_id']} "
                      f"(score: {result['prediction']:.3f})")

        # If we exit the loop, check if 'q' was pressed
        if processor.quit_requested:
            print("\n🛑 'q' was pressed - initiating graceful shutdown...")
            # Give a moment for any pending operations to complete
            time.sleep(1.0)

    except KeyboardInterrupt:
        print("\nReceived interrupt signal...")

    finally:
        # Clean shutdown
        processor.stop()
        print("System shutdown complete.")


def demo_with_custom_settings():
    """
    Demo function showing how to customize the processor settings
    """
    processor = LiveVideoProcessor(
        webcam_id=0,
        model_path='data/models/trained_model_224.pt',
        yolo_path='data/models/yolo11s.pt',
        output_dir='data/output/demo_clips',
        frame_buffer_size=16,  # Process every 16 frames
        crop_size=(224, 224),  # Model input size
        confidence_threshold=0.3  # Higher threshold for fewer false positives
    )

    print("Starting demo with custom settings...")
    processor.start()

    # Run for a specific duration
    try:
        time.sleep(60)  # Run for 1 minute
    except KeyboardInterrupt:
        pass

    processor.stop()

    # Print final statistics
    stats = processor.get_statistics()
    print(f"\nDemo completed!")
    print(f"Final stats: {stats}")


if __name__ == "__main__":
    main()
    